import{c as i,a as e,o as r,r as j,e as I,d as oe,q as P,h as y,j as d,M as R,g as o,A as S,k as H,t as l,C as M,F as U,s as q,y as L,S as ie,D as ue,_ as re,K as De,G as Ee,n as X,w as ze,T as Ie,J as Ne,H as Be,E as A,f as We,p as $e,R as Re,P as Le,b as ve,v as Ae,U as Ue,Q as Pe}from"./index-gGKuRSZs.js";import{r as fe}from"./MagnifyingGlassIcon-qd7t07FJ.js";import{r as ce}from"./DocumentTextIcon-BVdprHCE.js";function Ce(E,C){return r(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"})])}function ye(E,C){return r(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])}function pe(E,C){return r(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])}function ge(E,C){return r(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function _e(E,C){return r(),i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])}const se=j([]),be=j([]),ae=j(!1),Q=[{id:"WF001",name:"商品采集+智能裁图+批量刊登",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:156,status:"enabled",creator:"张三",createTime:"2024-01-15 10:30:00"},{id:"WF002",name:"一键抠图+超级裂变",apps:[{id:"app1",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"manual",timeout:25,onError:"retry"}},{id:"app2",name:"超级裂变",type:"super-split",settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"}}],usageCount:89,status:"enabled",creator:"李四",createTime:"2024-01-14 14:20:00"},{id:"WF003",name:"标题生成+POD合成+批量刊登",apps:[{id:"app1",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"manual",timeout:15,onError:"stop"}},{id:"app2",name:"POD合成",type:"pod-compose",settings:{mode:"auto",productSelection:"previous",timeout:45,onError:"retry"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:234,status:"disabled",creator:"王五",createTime:"2024-01-13 09:15:00"},{id:"WF004",name:"智能裁图+一键抠图",apps:[{id:"app1",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"manual",timeout:20,onError:"skip"}},{id:"app2",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}}],usageCount:67,status:"enabled",creator:"赵六",createTime:"2024-01-12 16:45:00"},{id:"WF005",name:"完整电商流程",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}},{id:"app4",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"previous",timeout:15,onError:"stop"}},{id:"app5",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:423,status:"enabled",creator:"孙七",createTime:"2024-01-11 11:20:00"}],Fe=()=>{se.value=[...Q]},Oe=()=>se.value,He=async E=>{ae.value=!0;try{await new Promise(z=>setTimeout(z,1e3));const C={id:`WF${String(se.value.length+1).padStart(3,"0")}`,name:E.name,apps:E.apps,usageCount:0,status:"enabled",creator:"当前用户",createTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})};return se.value.unshift(C),C}finally{ae.value=!1}},Ze=[{id:"EXE001",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:Q[0],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:35:00",duration:"5分钟",inputCount:0,outputCount:50},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-15 10:35:00",endTime:"2024-01-15 10:40:00",duration:"5分钟",inputCount:50,outputCount:50},{appId:"app3",appName:"批量刊登",status:"completed",startTime:"2024-01-15 10:40:00",endTime:"2024-01-15 10:45:00",duration:"5分钟",inputCount:50,outputCount:48}],startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:45:00",duration:"15分钟",executor:"张三"},{id:"EXE002",workflowId:"WF002",workflowName:"一键抠图+超级裂变",workflow:Q[1],status:"running",stepResults:[{appId:"app1",appName:"一键抠图",status:"completed",startTime:"2024-01-15 14:20:00",endTime:"2024-01-15 14:25:00",duration:"5分钟",inputCount:30,outputCount:30},{appId:"app2",appName:"超级裂变",status:"running",startTime:"2024-01-15 14:25:00",inputCount:30,outputCount:0}],startTime:"2024-01-15 14:20:00",duration:"8分钟",executor:"李四"},{id:"EXE003",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:Q[0],status:"failed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:05:00",duration:"5分钟",inputCount:0,outputCount:25},{appId:"app2",appName:"智能裁图",status:"failed",startTime:"2024-01-15 09:05:00",endTime:"2024-01-15 09:07:00",duration:"2分钟",inputCount:25,outputCount:0,errorMessage:"图片格式不支持"}],startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:07:00",duration:"7分钟",executor:"王五"},{id:"EXE004",workflowId:"WF005",workflowName:"完整电商流程",workflow:Q[4],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:10:00",duration:"10分钟",inputCount:0,outputCount:100},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-14 16:10:00",endTime:"2024-01-14 16:20:00",duration:"10分钟",inputCount:100,outputCount:100},{appId:"app3",appName:"一键抠图",status:"completed",startTime:"2024-01-14 16:20:00",endTime:"2024-01-14 16:30:00",duration:"10分钟",inputCount:100,outputCount:95},{appId:"app4",appName:"标题生成",status:"completed",startTime:"2024-01-14 16:30:00",endTime:"2024-01-14 16:35:00",duration:"5分钟",inputCount:95,outputCount:95},{appId:"app5",appName:"批量刊登",status:"completed",startTime:"2024-01-14 16:35:00",endTime:"2024-01-14 16:45:00",duration:"10分钟",inputCount:95,outputCount:92}],startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:45:00",duration:"45分钟",executor:"孙七"},{id:"EXE005",workflowId:"WF003",workflowName:"标题生成+POD合成+批量刊登",workflow:Q[2],status:"pending",stepResults:[{appId:"app1",appName:"标题生成",status:"pending"},{appId:"app2",appName:"POD合成",status:"pending"},{appId:"app3",appName:"批量刊登",status:"pending"}],startTime:"2024-01-15 15:00:00",executor:"赵六"}],Xe=async()=>{ae.value=!0;try{return await new Promise(E=>setTimeout(E,500)),be.value=[...Ze],be.value}finally{ae.value=!1}};I(()=>se.value),I(()=>be.value),I(()=>ae.value);const Ke={class:"mb-4"},qe={class:"grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[500px] overflow-y-auto"},Je=["onClick"],Ge={class:"flex justify-between items-start mb-3"},Qe={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Ye={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},et={class:"mb-3"},tt={class:"flex items-center space-x-2 overflow-x-auto pb-2"},st={class:"flex items-center space-x-1 flex-shrink-0"},at={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},ot={class:"flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/30 rounded px-2 py-1 flex-shrink-0"},rt={class:"text-xs text-blue-700 dark:text-blue-300"},lt={class:"flex items-center space-x-1 flex-shrink-0"},nt={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},dt={class:"flex justify-between items-center text-sm text-gray-500 dark:text-dark-text-secondary"},it={key:0,class:"text-center py-12"},ut={class:"flex justify-end space-x-3"},ct=oe({__name:"WorkflowTemplateDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select","create-blank"],setup(E,{emit:C}){const z=E,B=C,D=I({get:()=>z.modelValue,set:N=>B("update:modelValue",N)}),V=j(""),$=I(()=>Oe()),m=I(()=>V.value?$.value.filter(N=>N.name.toLowerCase().includes(V.value.toLowerCase())||N.apps.some(_=>_.name.toLowerCase().includes(V.value.toLowerCase()))):$.value),f=N=>({"product-collection":ue,"smart-crop":pe,"one-click-cutout":ge,"super-split":L,"title-generator":ce,"batch-listing":ie,"pod-compose":L})[N]||L,T=N=>{B("select",N),W()},x=()=>{B("create-blank"),W()},W=()=>{V.value="",D.value=!1};return(N,_)=>{const p=y("el-input"),u=y("el-tag"),b=y("el-button"),n=y("el-dialog");return r(),P(n,{modelValue:D.value,"onUpdate:modelValue":_[1]||(_[1]=v=>D.value=v),title:"工作流模板",width:"1000px","close-on-click-modal":!1,class:"template-dialog"},{footer:d(()=>[e("div",ut,[o(b,{onClick:W,size:"large"},{default:d(()=>_[5]||(_[5]=[M(" 取消 ")])),_:1,__:[5]}),o(b,{onClick:x,type:"primary",size:"large",plain:""},{default:d(()=>_[6]||(_[6]=[M(" 创建空白工作流 ")])),_:1,__:[6]})])]),default:d(()=>[e("div",Ke,[o(p,{modelValue:V.value,"onUpdate:modelValue":_[0]||(_[0]=v=>V.value=v),placeholder:"搜索模板...",size:"large",clearable:""},{prefix:d(()=>[o(S(fe),{class:"w-5 h-5 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",qe,[(r(!0),i(U,null,H(m.value,v=>(r(),i("div",{key:v.id,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-all duration-200",onClick:c=>T(v)},[e("div",Ge,[e("div",null,[e("h3",Qe,l(v.name),1),e("p",Ye,l(v.apps.length)+" 个应用 · 使用 "+l(v.usageCount)+" 次 ",1)]),o(u,{type:v.status==="enabled"?"success":"danger",size:"small"},{default:d(()=>[M(l(v.status==="enabled"?"可用":"禁用"),1)]),_:2},1032,["type"])]),e("div",et,[e("div",tt,[e("div",st,[e("div",at,[o(S(ye),{class:"w-3 h-3 text-white"})]),_[2]||(_[2]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),(r(!0),i(U,null,H(v.apps,(c,F)=>(r(),i(U,{key:F},[o(S(Ce),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",ot,[(r(),P(q(f(c.type)),{class:"w-3 h-3 text-blue-600 dark:text-blue-400"})),e("span",rt,l(c.name),1)])],64))),128)),o(S(Ce),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",lt,[e("div",nt,[o(S(_e),{class:"w-3 h-3 text-white"})]),_[3]||(_[3]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])]),e("div",dt,[e("span",null,"创建者："+l(v.creator),1),e("span",null,l(v.createTime),1)])],8,Je))),128))]),m.value.length===0?(r(),i("div",it,[o(S(L),{class:"w-16 h-16 mx-auto text-gray-400 mb-4"}),_[4]||(_[4]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的模板",-1))])):R("",!0)]),_:1},8,["modelValue"])}}}),pt=re(ct,[["__scopeId","data-v-56b174c9"]]),gt={class:"flex h-[600px]"},mt={class:"w-64 border-r border-gray-200 dark:border-dark-border pr-4"},xt={class:"mb-4"},kt={class:"flex justify-between items-center mb-2"},vt={class:"space-y-2 max-h-[500px] overflow-y-auto"},bt=["onDragstart"],ft={class:"flex-1 min-w-0"},yt={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},_t={class:"text-xs text-gray-500 dark:text-dark-text-secondary truncate"},wt={class:"flex-1 px-4"},ht={class:"mb-4"},$t={class:"flex flex-col items-center space-y-6"},Ct={class:"flex flex-col items-center space-y-2"},Vt={class:"w-16 h-16 bg-green-500 rounded-full flex items-center justify-center shadow-lg"},Tt={key:0,class:"flex flex-col items-center space-y-6 w-full"},jt={class:"w-full max-w-2xl"},St=["onClick"],Mt={class:"flex flex-col items-center space-y-2 p-3"},Dt={class:"w-14 h-14 bg-blue-500 rounded-full flex items-center justify-center relative shadow-md"},Et=["onClick"],zt={class:"text-xs text-gray-600 dark:text-dark-text-secondary font-medium text-center max-w-20 truncate"},It={class:"flex flex-col items-center space-y-2"},Nt={class:"w-16 h-16 bg-red-500 rounded-full flex items-center justify-center shadow-lg"},Bt={key:0,class:"absolute inset-0 flex items-center justify-center"},Wt={class:"text-center text-gray-500 dark:text-dark-text-secondary"},Rt={class:"w-80 border-l border-gray-200 dark:border-dark-border pl-4"},Lt={key:0,class:"space-y-4"},At={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Ut={class:"flex items-center space-x-3 mb-4"},Pt={class:"font-medium text-gray-900 dark:text-dark-text"},Ft={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ot={class:"space-y-4"},Ht={key:1,class:"text-center text-gray-500 dark:text-dark-text-secondary py-8"},Zt={class:"flex justify-end space-x-3"},Xt=oe({__name:"CreateWorkflowDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(E,{emit:C}){const z=E,B=C,D=I({get:()=>z.modelValue,set:k=>B("update:modelValue",k)}),V=j(!1),$=j(""),m=j(null),f=j(!1),T=De({name:""}),x=j([]),W=[{id:"product-collection",name:"商品采集",type:"product-collection",description:"采集电商平台商品信息"},{id:"smart-crop",name:"智能裁图",type:"smart-crop",description:"智能裁剪商品图片"},{id:"one-click-cutout",name:"一键抠图",type:"one-click-cutout",description:"自动抠图去背景"},{id:"super-split",name:"超级裂变",type:"super-split",description:"图片批量裂变处理"},{id:"title-generator",name:"标题生成",type:"title-generator",description:"智能生成商品标题"},{id:"batch-listing",name:"批量刊登",type:"batch-listing",description:"批量刊登商品到平台"},{id:"pod-compose",name:"POD合成",type:"pod-compose",description:"POD商品合成处理"}],N=I(()=>W.filter(k=>k.name.toLowerCase().includes($.value.toLowerCase())||k.description.toLowerCase().includes($.value.toLowerCase()))),_=I(()=>T.name.trim()&&x.value.length>0),p=k=>({"product-collection":ue,"smart-crop":pe,"one-click-cutout":ge,"super-split":L,"title-generator":ce,"batch-listing":ie,"pod-compose":L})[k]||L,u=()=>{A.info("跳转到应用市场功能开发中...")},b=k=>{T.name=`${k.name} - 副本`,x.value=k.apps.map(s=>{const O=W.find(J=>J.type===s.type);return{id:`app_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:s.name,type:s.type,description:(O==null?void 0:O.description)||"",settings:{...s.settings},datasetConfig:{}}}),f.value=!1,A.success(`已从模板"${k.name}"加载配置`)},n=(k,s)=>{k.dataTransfer&&(k.dataTransfer.setData("application/json",JSON.stringify(s)),k.dataTransfer.effectAllowed="copy")},v=k=>{k.preventDefault(),k.dataTransfer&&(k.dataTransfer.dropEffect="copy")},c=k=>{if(k.preventDefault(),k.dataTransfer)try{const s=JSON.parse(k.dataTransfer.getData("application/json"));F(s)}catch(s){console.error("Failed to parse dropped data:",s)}},F=k=>{const s={...k,id:`${k.id}_${Date.now()}`,settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"},datasetConfig:{}};x.value.push(s),m.value=x.value.length-1},K=k=>{x.value.splice(k,1),m.value===k?m.value=null:m.value!==null&&m.value>k&&m.value--},Y=()=>{m.value=null},h=async()=>{if(_.value){V.value=!0;try{await He({name:T.name,apps:x.value}),A.success("工作流创建成功！"),B("success"),w()}catch{A.error("创建失败，请重试")}finally{V.value=!1}}},w=()=>{T.name="",x.value=[],m.value=null,$.value="",f.value=!1,D.value=!1};return(k,s)=>{const O=y("el-button"),J=y("el-input"),le=y("el-radio"),me=y("el-radio-group"),Z=y("el-option"),ne=y("el-select"),xe=y("el-input-number"),ke=y("el-dialog");return r(),P(ke,{modelValue:D.value,"onUpdate:modelValue":s[10]||(s[10]=a=>D.value=a),title:"新建工作流",width:"1200px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"workflow-dialog"},{footer:d(()=>[e("div",Zt,[o(O,{onClick:w,size:"large"},{default:d(()=>s[28]||(s[28]=[M("取消")])),_:1,__:[28]}),o(O,{onClick:h,type:"primary",size:"large",loading:V.value,disabled:!_.value},{default:d(()=>[M(l(V.value?"创建中...":"确定创建"),1)]),_:1},8,["loading","disabled"])])]),default:d(()=>[e("div",gt,[e("div",mt,[e("div",xt,[e("div",kt,[s[13]||(s[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"可用应用",-1)),o(O,{onClick:u,type:"primary",size:"small",plain:""},{default:d(()=>s[11]||(s[11]=[M(" 应用市场 ")])),_:1,__:[11]}),o(O,{onClick:s[0]||(s[0]=a=>f.value=!0),size:"small",plain:""},{default:d(()=>s[12]||(s[12]=[M(" 选择模板 ")])),_:1,__:[12]})]),o(J,{modelValue:$.value,"onUpdate:modelValue":s[1]||(s[1]=a=>$.value=a),placeholder:"搜索应用...",size:"small",clearable:""},{prefix:d(()=>[o(S(fe),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",vt,[(r(!0),i(U,null,H(N.value,a=>(r(),i("div",{key:a.id,class:"flex items-center p-3 bg-gray-50 dark:bg-dark-card rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-dark-card/80 transition-colors duration-200",draggable:"true",onDragstart:t=>n(t,a)},[(r(),P(q(p(a.type)),{class:"w-5 h-5 text-blue-600 dark:text-blue-400 mr-3"})),e("div",ft,[e("div",yt,l(a.name),1),e("div",_t,l(a.description),1)]),o(S(Ee),{class:"w-4 h-4 text-gray-400"})],40,bt))),128))])]),e("div",wt,[e("div",ht,[s[14]||(s[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-2"},"工作流设计",-1)),o(J,{modelValue:T.name,"onUpdate:modelValue":s[2]||(s[2]=a=>T.name=a),placeholder:"请输入工作流名称...",size:"small",class:"mb-2"},null,8,["modelValue"])]),e("div",{class:"bg-white dark:bg-dark-surface rounded-lg p-6 min-h-[450px] border border-gray-200 dark:border-dark-border relative",onDrop:c,onDragover:v},[e("div",$t,[e("div",Ct,[e("div",Vt,[o(S(ye),{class:"w-8 h-8 text-white"})]),s[15]||(s[15]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"开始",-1))]),x.value.length>0?(r(),i("div",Tt,[s[16]||(s[16]=e("div",{class:"w-px h-8 bg-gray-300 dark:bg-gray-600"},null,-1)),e("div",jt,[o(S(Ne),{modelValue:x.value,"onUpdate:modelValue":s[3]||(s[3]=a=>x.value=a),onEnd:Y,"item-key":"id",animation:200,handle:".drag-handle","ghost-class":"sortable-ghost","chosen-class":"sortable-chosen","drag-class":"sortable-drag",class:"grid grid-cols-4 gap-4 justify-items-center"},{item:d(({element:a,index:t})=>[e("div",{class:X(["relative group cursor-pointer drag-handle",{"ring-2 ring-blue-500 rounded-lg":m.value===t}]),onClick:de=>m.value=t},[e("div",Mt,[e("div",Dt,[(r(),P(q(p(a.type)),{class:"w-7 h-7 text-white"})),e("button",{onClick:ze(de=>K(t),["stop"]),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 shadow-md"},[o(S(Ie),{class:"w-3 h-3 text-white"})],8,Et)]),e("span",zt,l(a.name),1)])],10,St)]),_:1},8,["modelValue"])]),s[17]||(s[17]=e("div",{class:"w-px h-8 bg-gray-300 dark:bg-gray-600"},null,-1))])):R("",!0),e("div",It,[e("div",Nt,[o(S(_e),{class:"w-8 h-8 text-white"})]),s[18]||(s[18]=e("span",{class:"text-sm font-medium text-gray-700 dark:text-dark-text"},"结束",-1))])]),x.value.length===0?(r(),i("div",Bt,[e("div",Wt,[o(S(L),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),s[19]||(s[19]=e("p",{class:"text-sm"},"拖拽左侧应用到此处开始构建工作流",-1))])])):R("",!0)],32)]),e("div",Rt,[s[27]||(s[27]=e("div",{class:"mb-4"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"应用设置")],-1)),m.value!==null&&x.value[m.value]?(r(),i("div",Lt,[e("div",At,[e("div",Ut,[(r(),P(q(p(x.value[m.value].type)),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"})),e("div",null,[e("div",Pt,l(x.value[m.value].name),1),e("div",Ft,l(x.value[m.value].description),1)])]),e("div",Ot,[e("div",null,[s[22]||(s[22]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"执行模式",-1)),o(me,{modelValue:x.value[m.value].settings.mode,"onUpdate:modelValue":s[4]||(s[4]=a=>x.value[m.value].settings.mode=a),size:"small"},{default:d(()=>[o(le,{label:"auto"},{default:d(()=>s[20]||(s[20]=[M("自动执行")])),_:1,__:[20]}),o(le,{label:"manual"},{default:d(()=>s[21]||(s[21]=[M("手动确认")])),_:1,__:[21]})]),_:1},8,["modelValue"])]),e("div",null,[s[23]||(s[23]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"产品选择",-1)),o(ne,{modelValue:x.value[m.value].settings.productSelection,"onUpdate:modelValue":s[5]||(s[5]=a=>x.value[m.value].settings.productSelection=a),placeholder:"选择产品来源",size:"small",class:"w-full"},{default:d(()=>[o(Z,{label:"使用上一步结果",value:"previous"}),o(Z,{label:"手动选择",value:"manual"}),o(Z,{label:"全部产品",value:"all"})]),_:1},8,["modelValue"])]),e("div",null,[s[24]||(s[24]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"超时时间（分钟）",-1)),o(xe,{modelValue:x.value[m.value].settings.timeout,"onUpdate:modelValue":s[6]||(s[6]=a=>x.value[m.value].settings.timeout=a),min:1,max:60,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[s[25]||(s[25]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"失败处理",-1)),o(ne,{modelValue:x.value[m.value].settings.onError,"onUpdate:modelValue":s[7]||(s[7]=a=>x.value[m.value].settings.onError=a),size:"small",class:"w-full"},{default:d(()=>[o(Z,{label:"停止工作流",value:"stop"}),o(Z,{label:"跳过继续",value:"skip"}),o(Z,{label:"重试",value:"retry"})]),_:1},8,["modelValue"])])])])])):(r(),i("div",Ht,[o(S(Be),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),s[26]||(s[26]=e("p",{class:"text-sm"},"选择一个应用节点进行设置",-1))]))])]),o(pt,{modelValue:f.value,"onUpdate:modelValue":s[8]||(s[8]=a=>f.value=a),onSelect:b,onCreateBlank:s[9]||(s[9]=a=>f.value=!1)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}}),Kt=re(Xt,[["__scopeId","data-v-7f899664"]]),qt={key:0,class:"space-y-6"},Jt={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Gt={class:"grid grid-cols-2 gap-4"},Qt={class:"text-sm text-gray-900 dark:text-dark-text"},Yt={class:"text-sm text-gray-900 dark:text-dark-text"},es={class:"text-sm text-gray-900 dark:text-dark-text"},ts={class:"text-sm text-gray-900 dark:text-dark-text"},ss={class:"text-sm text-gray-900 dark:text-dark-text"},as={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},os={class:"p-4"},rs={class:"space-y-4"},ls={class:"flex flex-col items-center"},ns={class:"flex-1 min-w-0"},ds={class:"flex items-center justify-between mb-2"},is={class:"flex items-center space-x-2"},us={class:"text-base font-medium text-gray-900 dark:text-dark-text"},cs={class:"grid grid-cols-2 gap-4 text-sm"},ps={key:0},gs={class:"text-gray-900 dark:text-dark-text"},ms={key:1},xs={class:"text-gray-900 dark:text-dark-text"},ks={key:2},vs={class:"text-gray-900 dark:text-dark-text"},bs={key:3},fs={class:"text-gray-900 dark:text-dark-text"},ys={key:0,class:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800"},_s={class:"text-sm text-red-700 dark:text-red-300"},ws={class:"flex justify-end"},hs=oe({__name:"ExecutionDetailsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(E,{emit:C}){const z=E,B=C,D=I({get:()=>z.modelValue,set:p=>B("update:modelValue",p)}),V=p=>({"product-collection":ue,"smart-crop":pe,"one-click-cutout":ge,"super-split":L,"title-generator":ce,"batch-listing":ie,"pod-compose":L})[p]||L,$=p=>({商品采集:"product-collection",智能裁图:"smart-crop",一键抠图:"one-click-cutout",超级裂变:"super-split",标题生成:"title-generator",批量刊登:"batch-listing",POD合成:"pod-compose"})[p]||"unknown",m=p=>{switch(p){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},f=p=>{switch(p){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},T=p=>m(p),x=p=>f(p),W=p=>{switch(p){case"completed":return"bg-green-500 text-white";case"failed":return"bg-red-500 text-white";case"running":return"bg-blue-500 text-white";case"pending":return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300";default:return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}},N=p=>{switch(p){case"completed":return"bg-green-300 dark:bg-green-600";case"failed":return"bg-red-300 dark:bg-red-600";case"running":return"bg-blue-300 dark:bg-blue-600";default:return"bg-gray-300 dark:bg-gray-600"}},_=()=>{D.value=!1};return(p,u)=>{const b=y("el-tag"),n=y("el-button"),v=y("el-dialog");return r(),P(v,{modelValue:D.value,"onUpdate:modelValue":u[0]||(u[0]=c=>D.value=c),title:"执行详情",width:"800px","close-on-click-modal":!1,class:"execution-dialog"},{footer:d(()=>[e("div",ws,[o(n,{onClick:_,size:"large"},{default:d(()=>u[14]||(u[14]=[M("关闭")])),_:1,__:[14]})])]),default:d(()=>[p.execution?(r(),i("div",qt,[e("div",Jt,[u[7]||(u[7]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"基本信息",-1)),e("div",Gt,[e("div",null,[u[1]||(u[1]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行ID",-1)),e("p",Qt,l(p.execution.id),1)]),e("div",null,[u[2]||(u[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"工作流名称",-1)),e("p",Yt,l(p.execution.workflowName),1)]),e("div",null,[u[3]||(u[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行状态",-1)),o(b,{type:m(p.execution.status),size:"small"},{default:d(()=>[M(l(f(p.execution.status)),1)]),_:1},8,["type"])]),e("div",null,[u[4]||(u[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行人",-1)),e("p",es,l(p.execution.executor),1)]),e("div",null,[u[5]||(u[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"开始时间",-1)),e("p",ts,l(p.execution.startTime),1)]),e("div",null,[u[6]||(u[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行时长",-1)),e("p",ss,l(p.execution.duration||"进行中"),1)])])]),e("div",as,[u[13]||(u[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text p-4 border-b border-gray-200 dark:border-dark-border"}," 执行步骤 ",-1)),e("div",os,[e("div",rs,[(r(!0),i(U,null,H(p.execution.stepResults,(c,F)=>(r(),i("div",{key:c.appId,class:"flex items-start space-x-4"},[e("div",ls,[e("div",{class:X(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",W(c.status)])},l(F+1),3),F<p.execution.stepResults.length-1?(r(),i("div",{key:0,class:X(["w-px h-12 mt-2",N(c.status)])},null,2)):R("",!0)]),e("div",ns,[e("div",ds,[e("div",is,[(r(),P(q(V($(c.appName))),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"})),e("h4",us,l(c.appName),1),o(b,{type:T(c.status),size:"small"},{default:d(()=>[M(l(x(c.status)),1)]),_:2},1032,["type"])])]),e("div",cs,[c.startTime?(r(),i("div",ps,[u[8]||(u[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"开始时间：",-1)),e("span",gs,l(c.startTime),1)])):R("",!0),c.duration?(r(),i("div",ms,[u[9]||(u[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"执行时长：",-1)),e("span",xs,l(c.duration),1)])):R("",!0),c.inputCount!==void 0?(r(),i("div",ks,[u[10]||(u[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输入数量：",-1)),e("span",vs,l(c.inputCount),1)])):R("",!0),c.outputCount!==void 0?(r(),i("div",bs,[u[11]||(u[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输出数量：",-1)),e("span",fs,l(c.outputCount),1)])):R("",!0)]),c.errorMessage?(r(),i("div",ys,[e("p",_s,[u[12]||(u[12]=e("strong",null,"错误信息：",-1)),M(l(c.errorMessage),1)])])):R("",!0)])]))),128))])])])])):R("",!0)]),_:1},8,["modelValue"])}}}),$s=re(hs,[["__scopeId","data-v-5b52e5e1"]]),Cs={key:0,class:"space-y-6"},Vs={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Ts={class:"grid grid-cols-4 gap-4"},js={class:"text-center"},Ss={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Ms={class:"text-center"},Ds={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Es={class:"text-center"},zs={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Is={class:"text-center"},Ns={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Bs={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},Ws={class:"p-4"},Rs={class:"space-y-4"},Ls={class:"grid grid-cols-3 gap-4"},As={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 text-center"},Us={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},Ps={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-center"},Fs={class:"text-lg font-semibold text-green-600 dark:text-green-400"},Os={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 text-center"},Hs={class:"text-lg font-semibold text-orange-600 dark:text-orange-400"},Zs={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Xs={key:0},Ks={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},qs=["src","alt"],Js={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},Gs={class:"text-sm text-green-600 dark:text-green-400 font-semibold"},Qs={key:1},Ys={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},ea=["src","alt"],ta={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center"},sa={key:2},aa={class:"space-y-2"},oa={class:"text-sm text-gray-900 dark:text-dark-text"},ra={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},la={key:3},na={class:"space-y-2"},da={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ia={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ua={key:4},ca={class:"flex justify-between"},pa=oe({__name:"ResultsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(E,{emit:C}){const z=E,B=C,D=I({get:()=>z.modelValue,set:b=>B("update:modelValue",b)}),V=j(""),$=I(()=>(z.execution&&z.execution.stepResults.length>0&&(V.value=z.execution.stepResults[0].appId),z.execution)),m=()=>{if(!$.value)return 0;const b=$.value.stepResults[0];return(b==null?void 0:b.inputCount)||0},f=()=>{if(!$.value)return 0;const b=$.value.stepResults[$.value.stepResults.length-1];return(b==null?void 0:b.outputCount)||0},T=()=>{const b=m(),n=f();return b===0?0:Math.round(n/b*100)},x=b=>Array.from({length:Math.min(b,6)},(n,v)=>({id:`product_${v+1}`,title:`商品标题 ${v+1} - 高质量产品描述`,price:(Math.random()*100+10).toFixed(2),image:`https://picsum.photos/200/200?random=${v+1}`})),W=b=>Array.from({length:Math.min(b,8)},(n,v)=>({id:`image_${v+1}`,name:`处理后图片_${v+1}.jpg`,image:`https://picsum.photos/150/150?random=${v+10}`})),N=b=>{const n=["高品质时尚T恤 - 舒适透气 多色可选 男女通用款式","精美陶瓷马克杯 - 创意设计 办公室必备 礼品首选","多功能手机壳 - 防摔保护 时尚外观 适配多机型","舒适运动鞋 - 轻便透气 专业运动 日常休闲两用","优质帆布包 - 大容量设计 环保材质 时尚百搭"];return Array.from({length:Math.min(b,5)},(v,c)=>({id:`title_${c+1}`,title:n[c%n.length],length:n[c%n.length].length}))},_=b=>{const n=["Amazon","eBay","Shopify","Etsy"];return Array.from({length:Math.min(b,8)},(v,c)=>({id:`listing_${c+1}`,platform:n[c%n.length],productId:`PRD${String(c+1).padStart(6,"0")}`,status:Math.random()>.2?"success":"failed"}))},p=()=>{A.success("结果下载功能开发中...")},u=()=>{D.value=!1};return(b,n)=>{const v=y("el-tag"),c=y("el-tab-pane"),F=y("el-tabs"),K=y("el-button"),Y=y("el-dialog");return r(),P(Y,{modelValue:D.value,"onUpdate:modelValue":n[1]||(n[1]=h=>D.value=h),title:"处理结果",width:"1000px","close-on-click-modal":!1,class:"results-dialog"},{footer:d(()=>[e("div",ca,[o(K,{onClick:p,type:"primary",plain:""},{default:d(()=>n[13]||(n[13]=[M(" 下载结果 ")])),_:1,__:[13]}),o(K,{onClick:u,size:"large"},{default:d(()=>n[14]||(n[14]=[M("关闭")])),_:1,__:[14]})])]),default:d(()=>[$.value?(r(),i("div",Cs,[e("div",Vs,[n[6]||(n[6]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"处理概览",-1)),e("div",Ts,[e("div",js,[e("div",Ss,l(m()),1),n[2]||(n[2]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输入",-1))]),e("div",Ms,[e("div",Ds,l(f()),1),n[3]||(n[3]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输出",-1))]),e("div",Es,[e("div",zs,l(T())+"%",1),n[4]||(n[4]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功率",-1))]),e("div",Is,[e("div",Ns,l($.value.duration||"0分钟"),1),n[5]||(n[5]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总耗时",-1))])])]),e("div",Bs,[n[12]||(n[12]=e("div",{class:"p-4 border-b border-gray-200 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"各步骤处理结果")],-1)),e("div",Ws,[o(F,{modelValue:V.value,"onUpdate:modelValue":n[0]||(n[0]=h=>V.value=h),type:"border-card"},{default:d(()=>[(r(!0),i(U,null,H($.value.stepResults,h=>(r(),P(c,{key:h.appId,label:h.appName,name:h.appId},{default:d(()=>[e("div",Rs,[e("div",Ls,[e("div",As,[e("div",Us,l(h.inputCount||0),1),n[7]||(n[7]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输入数量",-1))]),e("div",Ps,[e("div",Fs,l(h.outputCount||0),1),n[8]||(n[8]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输出数量",-1))]),e("div",Os,[e("div",Hs,l(h.duration||"0分钟"),1),n[9]||(n[9]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理时长",-1))])]),e("div",Zs,[n[11]||(n[11]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"},"处理结果数据",-1)),h.appName==="商品采集"?(r(),i("div",Xs,[e("div",Ks,[(r(!0),i(U,null,H(x(h.outputCount||0),w=>(r(),i("div",{key:w.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("img",{src:w.image,alt:w.title,class:"w-full h-32 object-cover rounded mb-2"},null,8,qs),e("h5",Js,l(w.title),1),e("p",Gs,"$"+l(w.price),1)]))),128))])])):h.appName==="智能裁图"||h.appName==="一键抠图"?(r(),i("div",Qs,[e("div",Ys,[(r(!0),i(U,null,H(W(h.outputCount||0),w=>(r(),i("div",{key:w.id,class:"bg-white dark:bg-dark-surface rounded-lg p-2 border border-gray-200 dark:border-dark-border"},[e("img",{src:w.image,alt:w.name,class:"w-full h-24 object-cover rounded mb-1"},null,8,ea),e("p",ta,l(w.name),1)]))),128))])])):h.appName==="标题生成"?(r(),i("div",sa,[e("div",aa,[(r(!0),i(U,null,H(N(h.outputCount||0),w=>(r(),i("div",{key:w.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("p",oa,l(w.title),1),e("p",ra,"长度: "+l(w.length)+" 字符",1)]))),128))])])):h.appName==="批量刊登"?(r(),i("div",la,[e("div",na,[(r(!0),i(U,null,H(_(h.outputCount||0),w=>(r(),i("div",{key:w.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border flex justify-between items-center"},[e("div",null,[e("p",da,l(w.platform),1),e("p",ia,l(w.productId),1)]),o(v,{type:w.status==="success"?"success":"danger",size:"small"},{default:d(()=>[M(l(w.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]))),128))])])):(r(),i("div",ua,n[10]||(n[10]=[e("p",{class:"text-gray-500 dark:text-dark-text-secondary text-center py-4"}," 暂无结果数据展示 ",-1)])))])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])])])):R("",!0)]),_:1},8,["modelValue"])}}}),ga=re(pa,[["__scopeId","data-v-392a9730"]]),ma={class:"space-y-6"},xa={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border"},ka={class:"flex items-center justify-between"},va={class:"flex items-center space-x-3"},ba={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},fa={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-all duration-200"},ya={class:"flex items-center justify-between"},_a={class:"text-2xl font-bold text-gray-900 dark:text-dark-text mt-1"},wa={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-all duration-200"},ha={class:"flex items-center justify-between"},$a={class:"text-2xl font-bold text-green-600 dark:text-green-400 mt-1"},Ca={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-all duration-200"},Va={class:"flex items-center justify-between"},Ta={class:"text-2xl font-bold text-purple-600 dark:text-purple-400 mt-1"},ja={class:"bg-white dark:bg-dark-card rounded-xl p-6 shadow-sm border border-gray-100 dark:border-dark-border hover:shadow-md transition-all duration-200"},Sa={class:"flex items-center justify-between"},Ma={class:"text-2xl font-bold text-orange-600 dark:text-orange-400 mt-1"},Da={class:"bg-white dark:bg-dark-card rounded-xl shadow-sm border border-gray-100 dark:border-dark-border p-6"},Ea={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4"},za={class:"flex flex-col sm:flex-row gap-4"},Ia={class:"relative"},Na={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},Ba={class:"flex items-center justify-between sm:justify-end gap-4"},Wa={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ra={key:0,class:"flex items-center gap-2"},La={class:"text-sm text-blue-600 dark:text-blue-400"},Aa={class:"bg-white dark:bg-dark-card rounded-xl shadow-sm border border-gray-100 dark:border-dark-border overflow-hidden"},Ua={class:"flex items-center space-x-3"},Pa={class:"font-medium text-gray-900 dark:text-dark-text"},Fa={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Oa={class:"py-2"},Ha={class:"flex items-center space-x-2 max-w-full"},Za={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Xa={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},Ka={class:"flex items-center space-x-1 min-w-0 flex-1"},qa={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Ja={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center max-w-12 truncate"},Ga={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Qa={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},Ya={class:"flex flex-col items-center space-y-1 flex-shrink-0"},eo={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},to={class:"space-y-2"},so={key:0,class:"text-xs text-gray-500 dark:text-dark-text-secondary"},ao={class:"space-y-1"},oo={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},ro={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},lo={class:"flex flex-col space-y-2"},no=["onClick"],io={class:"px-6 py-4 border-t border-gray-100 dark:border-dark-border"},uo=oe({__name:"index",setup(E){const C=j(""),z=j(""),B=j([]),D=j(!1),V=j(!1),$=j(!1),m=j(null),f=j({currentPage:1,pageSize:20,total:0}),T=j([]),x=j(!1),W=I(()=>{let a=T.value;return C.value&&(a=a.filter(t=>t.workflowName.toLowerCase().includes(C.value.toLowerCase())||t.id.toLowerCase().includes(C.value.toLowerCase()))),z.value&&(a=a.filter(t=>t.status===z.value)),a}),N=I(()=>{const a=(f.value.currentPage-1)*f.value.pageSize,t=a+f.value.pageSize;return W.value.slice(a,t)}),_=I(()=>T.value.length),p=I(()=>{if(T.value.length===0)return 0;const a=T.value.filter(t=>t.status==="completed").length;return Math.round(a/T.value.length*100)}),u=I(()=>new Set(T.value.map(t=>t.workflowId)).size),b=I(()=>T.value.filter(t=>t.status==="completed"&&t.duration).length===0?"0分钟":"3.5分钟"),n=a=>({"product-collection":ue,"smart-crop":pe,"one-click-cutout":ge,"super-split":L,"title-generator":ce,"batch-listing":ie,"pod-compose":L})[a]||L,v=a=>{switch(a){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},c=async()=>{x.value=!0;try{const a=await Xe();T.value=a,f.value.total=W.value.length}catch{A.error("加载执行历史失败")}finally{x.value=!1}},F=a=>{B.value=a},K=()=>{f.value.currentPage=1,f.value.total=W.value.length},Y=a=>{m.value=a,V.value=!0},h=a=>{m.value=a,$.value=!0},w=a=>{switch(a){case"completed":return"bg-green-100 dark:bg-green-900/30";case"failed":return"bg-red-100 dark:bg-red-900/30";case"running":return"bg-blue-100 dark:bg-blue-900/30";case"pending":return"bg-gray-100 dark:bg-gray-900/30";default:return"bg-gray-100 dark:bg-gray-900/30"}},k=a=>{switch(a){case"completed":return"svg";case"failed":return"svg";case"running":return"svg";default:return"svg"}},s=a=>{switch(a){case"completed":return"text-green-600 dark:text-green-400";case"failed":return"text-red-600 dark:text-red-400";case"running":return"text-blue-600 dark:text-blue-400";case"pending":return"text-gray-600 dark:text-gray-400";default:return"text-gray-600 dark:text-gray-400"}},O=a=>{switch(a){case"completed":return"bg-green-500";case"failed":return"bg-red-500";case"running":return"bg-blue-500";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},J=a=>{switch(a){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";case"failed":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"running":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"pending":return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},le=a=>{switch(a){case"completed":return"bg-green-400";case"failed":return"bg-red-400";case"running":return"bg-blue-400";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},me=(a,t)=>{switch(a){case"viewResults":h(t);break;case"rerun":A.info("重新执行功能开发中...");break;case"duplicate":A.info("复制工作流功能开发中...");break;case"export":A.info("导出结果功能开发中...");break;case"delete":A.info("删除记录功能开发中...");break}},Z=()=>{A.success("导出执行历史功能开发中...")},ne=()=>{A.success("操作成功！"),c()},xe=a=>{f.value.pageSize=a,f.value.currentPage=1,c()},ke=a=>{f.value.currentPage=a,c()};return We(()=>{Fe(),c()}),(a,t)=>{const de=y("el-button"),G=y("el-table-column"),ee=y("el-dropdown-item"),Ve=y("el-dropdown-menu"),Te=y("el-dropdown"),je=y("el-table"),Se=y("el-pagination"),Me=Pe("loading");return r(),i("div",ma,[e("div",xa,[e("div",ka,[t[11]||(t[11]=$e('<div class="flex items-center space-x-4" data-v-6bc94a05><div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center shadow-lg" data-v-6bc94a05><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-6bc94a05><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-6bc94a05></path></svg></div><div data-v-6bc94a05><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-6bc94a05>工作流管理</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-6bc94a05>创建、管理和监控您的自动化工作流程</p></div></div>',1)),e("div",va,[o(de,{onClick:t[0]||(t[0]=g=>D.value=!0),type:"primary",size:"default",icon:S(Re),class:"shadow-sm"},{default:d(()=>t[9]||(t[9]=[M(" 新建工作流 ")])),_:1,__:[9]},8,["icon"]),o(de,{onClick:Z,size:"default",icon:S(Le),class:"shadow-sm"},{default:d(()=>t[10]||(t[10]=[M(" 导出数据 ")])),_:1,__:[10]},8,["icon"])])])]),e("div",ba,[e("div",fa,[e("div",ya,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总执行次数",-1)),e("p",_a,l(_.value),1)]),t[13]||(t[13]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",wa,[e("div",ha,[e("div",null,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",$a,l(p.value)+"%",1)]),t[15]||(t[15]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Ca,[e("div",Va,[e("div",null,[t[16]||(t[16]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"活跃工作流",-1)),e("p",Ta,l(u.value),1)]),t[17]||(t[17]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",ja,[e("div",Sa,[e("div",null,[t[18]||(t[18]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均耗时",-1)),e("p",Ma,l(b.value),1)]),t[19]||(t[19]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Da,[e("div",Ea,[e("div",za,[e("div",Ia,[e("div",Na,[o(S(fe),{class:"w-5 h-5 text-gray-400"})]),ve(e("input",{"onUpdate:modelValue":t[1]||(t[1]=g=>C.value=g),onInput:K,type:"text",placeholder:"搜索工作流名称或ID...",class:"w-full sm:w-80 pl-10 pr-4 py-2.5 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200 text-gray-900 dark:text-dark-text placeholder-gray-500"},null,544),[[Ae,C.value]])]),ve(e("select",{"onUpdate:modelValue":t[2]||(t[2]=g=>z.value=g),onChange:K,class:"px-3 py-2.5 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-200"},t[20]||(t[20]=[$e('<option value="" data-v-6bc94a05>全部状态</option><option value="completed" data-v-6bc94a05>已完成</option><option value="running" data-v-6bc94a05>执行中</option><option value="failed" data-v-6bc94a05>失败</option><option value="pending" data-v-6bc94a05>等待中</option>',5)]),544),[[Ue,z.value]])]),e("div",Ba,[e("div",Wa," 共 "+l(f.value.total)+" 条记录 ",1),B.value.length>0?(r(),i("div",Ra,[e("span",La,"已选择 "+l(B.value.length)+" 项",1),e("button",{onClick:t[3]||(t[3]=()=>S(A).info("批量操作功能开发中...")),class:"px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200"}," 批量操作 ")])):R("",!0)])])]),e("div",Aa,[t[33]||(t[33]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"执行历史")],-1)),ve((r(),P(je,{data:N.value,onSelectionChange:F,class:"w-full workflow-table","header-cell-style":{backgroundColor:"transparent",color:"#6b7280",fontWeight:"500",borderBottom:"1px solid #f3f4f6"},"row-style":{height:"72px"}},{default:d(()=>[o(G,{type:"selection",width:"55"}),o(G,{label:"执行信息","min-width":"250"},{default:d(g=>[e("div",Ua,[e("div",{class:X(["w-10 h-10 rounded-lg flex items-center justify-center",w(g.row.status)])},[(r(),P(q(k(g.row.status)),{class:X(["w-5 h-5",s(g.row.status)])},null,8,["class"]))],2),e("div",null,[e("div",Pa,l(g.row.workflowName),1),e("div",Fa,"ID: "+l(g.row.id),1)])])]),_:1}),o(G,{label:"工作流程",width:"280"},{default:d(({row:g})=>[e("div",Oa,[e("div",Ha,[e("div",Za,[e("div",Xa,[o(S(ye),{class:"w-3 h-3 text-white"})]),t[21]||(t[21]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),e("div",Ka,[(r(!0),i(U,null,H(g.workflow.apps.slice(0,3),(te,we)=>{var he;return r(),i(U,{key:we},[t[22]||(t[22]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",qa,[e("div",{class:X(["w-6 h-6 rounded-full flex items-center justify-center",O((he=g.stepResults[we])==null?void 0:he.status)])},[(r(),P(q(n(te.type)),{class:"w-3 h-3 text-white"}))],2),e("span",Ja,l(te.name),1)])],64)}),128)),g.workflow.apps.length>3?(r(),i(U,{key:0},[t[24]||(t[24]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",Ga,[t[23]||(t[23]=e("div",{class:"w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center"},[e("span",{class:"text-xs text-white font-bold"},"...")],-1)),e("span",Qa," +"+l(g.workflow.apps.length-3),1)])],64)):R("",!0)]),t[26]||(t[26]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",Ya,[e("div",eo,[o(S(_e),{class:"w-3 h-3 text-white"})]),t[25]||(t[25]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])])]),_:1}),o(G,{label:"执行状态",width:"150"},{default:d(g=>[e("div",to,[e("span",{class:X(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",J(g.row.status)])},[e("span",{class:X(["w-1.5 h-1.5 rounded-full mr-1.5",le(g.row.status)])},null,2),M(" "+l(v(g.row.status)),1)],2),g.row.duration?(r(),i("div",so," 耗时: "+l(g.row.duration),1)):R("",!0)])]),_:1}),o(G,{label:"执行信息",width:"180"},{default:d(g=>[e("div",ao,[e("div",oo,l(g.row.executor),1),e("div",ro,l(g.row.startTime),1)])]),_:1}),o(G,{label:"操作",width:"180"},{default:d(g=>[e("div",lo,[e("button",{onClick:te=>Y(g.row),class:"inline-flex items-center justify-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200 w-full"}," 查看详情 ",8,no),o(Te,{onCommand:te=>me(te,g.row),trigger:"click",class:"w-full"},{dropdown:d(()=>[o(Ve,null,{default:d(()=>[o(ee,{command:"viewResults",disabled:g.row.status!=="completed"},{default:d(()=>t[27]||(t[27]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("span",null,"处理结果")],-1)])),_:2,__:[27]},1032,["disabled"]),o(ee,{command:"rerun"},{default:d(()=>t[28]||(t[28]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),e("span",null,"重新执行")],-1)])),_:1,__:[28]}),o(ee,{command:"duplicate"},{default:d(()=>t[29]||(t[29]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})]),e("span",null,"复制工作流")],-1)])),_:1,__:[29]}),o(ee,{command:"export"},{default:d(()=>t[30]||(t[30]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),e("span",null,"导出结果")],-1)])),_:1,__:[30]}),o(ee,{command:"delete",divided:""},{default:d(()=>t[31]||(t[31]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})]),e("span",null,"删除记录")],-1)])),_:1,__:[31]})]),_:2},1024)]),default:d(()=>[t[32]||(t[32]=e("button",{class:"inline-flex items-center justify-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200 w-full"},[M(" 更多操作 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[32]},1032,["onCommand"])])]),_:1})]),_:1},8,["data"])),[[Me,x.value]]),e("div",io,[o(Se,{"current-page":f.value.currentPage,"onUpdate:currentPage":t[4]||(t[4]=g=>f.value.currentPage=g),"page-size":f.value.pageSize,"onUpdate:pageSize":t[5]||(t[5]=g=>f.value.pageSize=g),total:f.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:xe,onCurrentChange:ke},null,8,["current-page","page-size","total"])])]),o(Kt,{modelValue:D.value,"onUpdate:modelValue":t[6]||(t[6]=g=>D.value=g),onSuccess:ne},null,8,["modelValue"]),o($s,{modelValue:V.value,"onUpdate:modelValue":t[7]||(t[7]=g=>V.value=g),execution:m.value,onViewResults:h},null,8,["modelValue","execution"]),o(ga,{modelValue:$.value,"onUpdate:modelValue":t[8]||(t[8]=g=>$.value=g),execution:m.value},null,8,["modelValue","execution"])])}}}),mo=re(uo,[["__scopeId","data-v-6bc94a05"]]);export{mo as default};
