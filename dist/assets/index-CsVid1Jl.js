import{d as Q,r,e as T,c as i,a as t,g as n,p as W,t as d,M as b,C as R,j as C,h as f,F as I,k as X,E as g,n as j,w as S,o as u,_ as Y}from"./index-D_iwS02E.js";import{I as Z}from"./ImagePreviewDialog-DZn9Pzr_.js";const ee={class:"space-y-6"},te={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},se={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ae={class:"flex items-center justify-between"},oe={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},re={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},le={class:"flex items-center justify-between"},ne={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},de={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ie={class:"flex items-center justify-between"},ue={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ce={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},pe={class:"flex items-center justify-between"},ge={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},me={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},he={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},ve={class:"flex items-center space-x-3"},xe={key:0,class:"flex items-center space-x-2"},ke={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},be={class:"flex items-center space-x-3"},fe={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},we={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},ye={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},_e={class:"p-6"},Ce={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"},je=["onClick"],Me={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-orange-500 rounded-full flex items-center justify-center z-10"},ze={class:"relative"},Ve={key:0,class:"absolute inset-0 checkerboard-bg"},Be=["src","alt"],Te={class:"absolute top-2 left-2"},Re={class:"absolute bottom-2 left-2"},Ie={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center"},Se={class:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2"},Le=["onClick"],Ke=["onClick"],De={class:"mt-2"},Ee=["title"],Ue={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},$e={key:0,class:"flex justify-center mt-6"},He=Q({__name:"index",setup(Ne){const l=r([]),m=r(""),h=r(""),v=r(""),x=r(1),c=r(24),w=r(!1),M=r(null),L=r(179),K=r(89),D=r(56),E=r(34),y=r([{id:"result_001",name:"抠图结果_商品001.png",thumbnail:"https://picsum.photos/300/300?random=31",url:"https://picsum.photos/800/800?random=31",type:"cutout",status:"success",size:"456KB",processTime:"2024-01-15 14:30",hasTransparency:!0,originalImage:"https://picsum.photos/800/800?random=301",tags:["抠图","商品","透明背景"]},{id:"result_002",name:"裁图结果_商品002.jpg",thumbnail:"https://picsum.photos/300/300?random=32",url:"https://picsum.photos/800/800?random=32",type:"crop",status:"success",size:"234KB",processTime:"2024-01-15 14:25",hasTransparency:!1,originalImage:"https://picsum.photos/800/800?random=302",tags:["裁图","商品","智能裁剪"]},{id:"result_003",name:"裂变结果_商品003_1.jpg",thumbnail:"https://picsum.photos/300/300?random=33",url:"https://picsum.photos/800/800?random=33",type:"split",status:"success",size:"345KB",processTime:"2024-01-15 14:20",hasTransparency:!1,originalImage:"https://picsum.photos/800/800?random=303",tags:["裂变","商品","变体1"]},{id:"result_004",name:"抠图结果_商品004.png",thumbnail:"https://picsum.photos/300/300?random=34",url:"https://picsum.photos/800/800?random=34",type:"cutout",status:"failed",size:"0KB",processTime:"2024-01-15 13:45",hasTransparency:!0,originalImage:"https://picsum.photos/800/800?random=304",tags:["抠图","商品","处理失败"]},{id:"result_005",name:"裁图结果_商品005.jpg",thumbnail:"https://picsum.photos/300/300?random=35",url:"https://picsum.photos/800/800?random=35",type:"crop",status:"success",size:"567KB",processTime:"2024-01-15 13:40",hasTransparency:!1,originalImage:"https://picsum.photos/800/800?random=305",tags:["裁图","商品","高质量"]},{id:"result_006",name:"裂变结果_商品006_2.jpg",thumbnail:"https://picsum.photos/300/300?random=36",url:"https://picsum.photos/800/800?random=36",type:"split",status:"success",size:"423KB",processTime:"2024-01-15 13:35",hasTransparency:!1,originalImage:"https://picsum.photos/800/800?random=306",tags:["裂变","商品","变体2"]}]),k=T(()=>{let s=y.value;if(h.value&&(s=s.filter(e=>e.type===h.value)),v.value&&(s=s.filter(e=>e.status===v.value)),m.value){const e=m.value.toLowerCase();s=s.filter(o=>{var p;return o.name.toLowerCase().includes(e)||((p=o.tags)==null?void 0:p.some(_=>_.toLowerCase().includes(e)))})}return s}),U=T(()=>{const s=(x.value-1)*c.value,e=s+c.value;return k.value.slice(s,e)}),z=s=>l.value.some(e=>e.id===s.id),$=s=>{const e=l.value.findIndex(o=>o.id===s.id);e>-1?l.value.splice(e,1):l.value.push(s)},H=s=>({cutout:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",crop:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",split:"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",N=s=>({cutout:"抠图",crop:"裁图",split:"裂变"})[s]||"未知",P=s=>({success:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",failed:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"})[s]||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",A=s=>({success:"成功",failed:"失败"})[s]||"未知",F=s=>{M.value=s,w.value=!0},V=s=>{if(s.status==="failed"){g.warning("处理失败的结果无法下载");return}const e=document.createElement("a");e.href=s.url,e.download=s.name,document.body.appendChild(e),e.click(),document.body.removeChild(e),g.success(`正在下载 ${s.name}`)},q=()=>{if(l.value.length===0)return;const s=l.value.filter(e=>e.status==="success");if(s.length===0){g.warning("没有可下载的成功结果");return}s.forEach(e=>{V(e)}),g.success(`正在下载 ${s.length} 个结果`)},G=()=>{l.value.length!==0&&(l.value.forEach(s=>{const e=y.value.findIndex(o=>o.id===s.id);e>-1&&y.value.splice(e,1)}),g.success(`已删除 ${l.value.length} 个结果`),l.value=[])},J=()=>{x.value=1};return(s,e)=>{const o=f("el-option"),p=f("el-select"),_=f("el-input"),O=f("el-pagination");return u(),i(I,null,[t("div",ee,[e[21]||(e[21]=W('<div class="bg-gradient-to-r from-orange-50 to-red-50 dark:from-orange-900/20 dark:to-red-900/20 rounded-2xl p-6 border border-orange-100 dark:border-orange-800" data-v-a81842af><div class="flex items-center space-x-3" data-v-a81842af><div class="w-10 h-10 bg-gradient-to-br from-orange-500 to-red-600 rounded-xl flex items-center justify-center" data-v-a81842af><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-a81842af><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" data-v-a81842af></path></svg></div><div data-v-a81842af><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-a81842af>处理结果</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-a81842af>管理AI处理后的图片结果</p></div></div></div>',1)),t("div",te,[t("div",se,[t("div",ae,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理结果总数",-1)),t("p",oe,d(L.value),1)]),e[7]||(e[7]=t("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})])],-1))])]),t("div",re,[t("div",le,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"抠图结果",-1)),t("p",ne,d(K.value),1)]),e[9]||(e[9]=t("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M19 10a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),t("div",de,[t("div",ie,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"裁图结果",-1)),t("p",ue,d(D.value),1)]),e[11]||(e[11]=t("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),t("div",ce,[t("div",pe,[t("div",null,[e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"裂变结果",-1)),t("p",ge,d(E.value),1)]),e[13]||(e[13]=t("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})])],-1))])])]),t("div",me,[t("div",he,[t("div",ve,[l.value.length>0?(u(),i("div",xe,[t("span",ke," 已选择 "+d(l.value.length)+" 个结果 ",1),t("button",{onClick:q,class:"inline-flex items-center px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},e[14]||(e[14]=[t("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),R(" 批量下载 ")])),t("button",{onClick:G,class:"inline-flex items-center px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},e[15]||(e[15]=[t("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),R(" 批量删除 ")]))])):b("",!0)]),t("div",be,[n(p,{modelValue:h.value,"onUpdate:modelValue":e[0]||(e[0]=a=>h.value=a),placeholder:"处理类型",style:{width:"120px"},clearable:""},{default:C(()=>[n(o,{label:"全部",value:""}),n(o,{label:"智能抠图",value:"cutout"}),n(o,{label:"智能裁图",value:"crop"}),n(o,{label:"超级裂变",value:"split"})]),_:1},8,["modelValue"]),n(p,{modelValue:v.value,"onUpdate:modelValue":e[1]||(e[1]=a=>v.value=a),placeholder:"状态",style:{width:"100px"},clearable:""},{default:C(()=>[n(o,{label:"全部",value:""}),n(o,{label:"成功",value:"success"}),n(o,{label:"失败",value:"failed"})]),_:1},8,["modelValue"]),n(_,{modelValue:m.value,"onUpdate:modelValue":e[2]||(e[2]=a=>m.value=a),placeholder:"搜索结果...",style:{width:"200px"},clearable:"",onInput:J},{prefix:C(()=>e[16]||(e[16]=[t("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"])])])]),t("div",fe,[t("div",we,[e[17]||(e[17]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"处理结果",-1)),t("p",ye,"共 "+d(k.value.length)+" 个结果",1)]),t("div",_e,[t("div",Ce,[(u(!0),i(I,null,X(U.value,a=>(u(),i("div",{key:a.id,class:"group relative cursor-pointer",onClick:B=>$(a)},[z(a)?(u(),i("div",Me,e[18]||(e[18]=[t("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):b("",!0),t("div",ze,[t("div",{class:j(["w-full h-32 rounded-lg border-2 transition-all duration-200 overflow-hidden",z(a)?"border-orange-500":"border-gray-200 dark:border-dark-border group-hover:border-orange-300"])},[a.hasTransparency?(u(),i("div",Ve)):b("",!0),t("img",{src:a.thumbnail,alt:a.name,class:"w-full h-full object-contain relative z-10"},null,8,Be)],2),t("div",Te,[t("span",{class:j([H(a.type),"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"])},d(N(a.type)),3)]),t("div",Re,[t("span",{class:j([P(a.status),"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"])},d(A(a.status)),3)]),t("div",Ie,[t("div",Se,[t("button",{onClick:S(B=>F(a),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},e[19]||(e[19]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)]),8,Le),t("button",{onClick:S(B=>V(a),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},e[20]||(e[20]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1)]),8,Ke)])])]),t("div",De,[t("p",{class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate",title:a.name},d(a.name),9,Ee),t("p",Ue,d(a.size)+" • "+d(a.processTime),1)])],8,je))),128))]),k.value.length>c.value?(u(),i("div",$e,[n(O,{"current-page":x.value,"onUpdate:currentPage":e[3]||(e[3]=a=>x.value=a),"page-size":c.value,"onUpdate:pageSize":e[4]||(e[4]=a=>c.value=a),"page-sizes":[24,48,96],total:k.value.length,layout:"sizes, prev, pager, next",class:"modern-pagination"},null,8,["current-page","page-size","total"])])):b("",!0)])])]),n(Z,{modelValue:w.value,"onUpdate:modelValue":e[5]||(e[5]=a=>w.value=a),image:M.value},null,8,["modelValue","image"])],64)}}}),Fe=Y(He,[["__scopeId","data-v-a81842af"]]);export{Fe as default};
