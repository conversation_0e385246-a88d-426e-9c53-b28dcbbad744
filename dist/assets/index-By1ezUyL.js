import{d as Q,e as K,r as s,q as le,j,h as w,a as e,l as S,g as i,c as m,k as T,n as H,t as p,F as U,M as D,C as I,o as u,_ as W,p as se,E as R,w as J}from"./index-gGKuRSZs.js";import{I as de}from"./ImagePreviewDialog-6Epq2YXu.js";const ne={class:"p-6 space-y-6"},ie={class:"space-y-3"},ue={class:"space-y-3"},ce={class:"space-y-3"},ge={class:"grid grid-cols-4 gap-3"},me=["onClick"],ve={class:"text-center"},pe={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},xe={class:"space-y-3"},fe={class:"space-y-4"},he={class:"text-sm text-gray-600 dark:text-dark-text-secondary w-12"},ke=["onClick"],be={class:"space-y-3"},ye={class:"grid grid-cols-4 gap-3"},we=["onClick"],_e={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center"},Ce={class:"text-xs text-white opacity-0 group-hover:opacity-100 transition-opacity duration-200 font-medium"},Ve={class:"flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50 space-x-3"},Be=["disabled"],je=Q({__name:"GradientCreatorDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","create"],setup(q,{emit:N}){const P=q,v=N,_=K({get:()=>P.modelValue,set:c=>v("update:modelValue",c)}),x=s(""),f=s("135deg"),n=s(["#667eea","#764ba2"]),C=s([{value:"0deg",label:"向上",preview:"linear-gradient(0deg, #667eea, #764ba2)"},{value:"90deg",label:"向右",preview:"linear-gradient(90deg, #667eea, #764ba2)"},{value:"180deg",label:"向下",preview:"linear-gradient(180deg, #667eea, #764ba2)"},{value:"270deg",label:"向左",preview:"linear-gradient(270deg, #667eea, #764ba2)"},{value:"45deg",label:"右上",preview:"linear-gradient(45deg, #667eea, #764ba2)"},{value:"135deg",label:"右下",preview:"linear-gradient(135deg, #667eea, #764ba2)"},{value:"225deg",label:"左下",preview:"linear-gradient(225deg, #667eea, #764ba2)"},{value:"315deg",label:"左上",preview:"linear-gradient(315deg, #667eea, #764ba2)"}]),z=s([{name:"蓝紫",style:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)"},{name:"橙红",style:"linear-gradient(135deg, #f093fb 0%, #f5576c 100%)"},{name:"绿蓝",style:"linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)"},{name:"粉紫",style:"linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)"},{name:"黄橙",style:"linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%)"},{name:"蓝绿",style:"linear-gradient(135deg, #a8e6cf 0%, #dcedc1 100%)"},{name:"紫粉",style:"linear-gradient(135deg, #fbc2eb 0%, #a6c1ee 100%)"},{name:"日落",style:"linear-gradient(135deg, #fa709a 0%, #fee140 100%)"}]),$=K(()=>{const c=n.value.map((o,y)=>{const M=y/(n.value.length-1)*100;return`${o} ${M}%`}).join(", ");return`linear-gradient(${f.value}, ${c})`}),F=()=>{n.value.push("#ffffff")},E=c=>{n.value.length>2&&n.value.splice(c,1)},A=c=>{const o=c.style.match(/linear-gradient\(([^,]+),\s*([^)]+)\)/);if(o){const y=o[1].trim(),V=o[2].match(/#[0-9a-fA-F]{6}/g);V&&V.length>=2&&(f.value=y,n.value=V.slice(0,5),x.value=`${c.name}渐变`)}},G=()=>{x.value.trim()&&(v("create",{name:x.value,style:$.value}),L())},L=()=>{x.value="",f.value="135deg",n.value=["#667eea","#764ba2"],v("update:modelValue",!1)};return(c,o)=>{const y=w("el-input"),M=w("el-color-picker"),V=w("el-dialog");return u(),le(V,{modelValue:_.value,"onUpdate:modelValue":o[1]||(o[1]=g=>_.value=g),width:"600px","align-center":"","show-close":!1,class:"modern-dialog"},{header:j(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[o[3]||(o[3]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"创建渐变背景"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"自定义渐变色彩和方向")])],-1)),e("button",{onClick:L,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},o[2]||(o[2]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:j(()=>[e("div",Ve,[e("button",{onClick:L,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 取消 "),e("button",{onClick:G,disabled:!x.value.trim(),class:"px-6 py-2.5 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"}," 创建渐变 ",8,Be)])]),default:j(()=>[e("div",ne,[e("div",ie,[o[4]||(o[4]=e("label",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"预览效果",-1)),e("div",{class:"w-full h-32 rounded-lg border border-gray-200 dark:border-dark-border",style:S({background:$.value})},null,4)]),e("div",ue,[o[5]||(o[5]=e("label",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"渐变名称",-1)),i(y,{modelValue:x.value,"onUpdate:modelValue":o[0]||(o[0]=g=>x.value=g),placeholder:"输入渐变名称...",clearable:""},null,8,["modelValue"])]),e("div",ce,[o[6]||(o[6]=e("label",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"渐变方向",-1)),e("div",ge,[(u(!0),m(U,null,T(C.value,g=>(u(),m("button",{key:g.value,onClick:h=>f.value=g.value,class:H(["p-3 border-2 rounded-lg transition-all duration-200 hover:shadow-md",f.value===g.value?"border-green-500 bg-green-50 dark:bg-green-900/20":"border-gray-200 dark:border-dark-border"])},[e("div",ve,[e("div",{class:"w-8 h-8 mx-auto mb-2 rounded",style:S({background:g.preview})},null,4),e("span",pe,p(g.label),1)])],10,me))),128))])]),e("div",xe,[o[9]||(o[9]=e("label",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"渐变颜色",-1)),e("div",fe,[(u(!0),m(U,null,T(n.value,(g,h)=>(u(),m("div",{key:h,class:"flex items-center space-x-3"},[e("span",he,"颜色"+p(h+1),1),i(M,{modelValue:n.value[h],"onUpdate:modelValue":B=>n.value[h]=B},null,8,["modelValue","onUpdate:modelValue"]),i(y,{modelValue:n.value[h],"onUpdate:modelValue":B=>n.value[h]=B,placeholder:"#000000",style:{width:"120px"}},null,8,["modelValue","onUpdate:modelValue"]),n.value.length>2?(u(),m("button",{key:0,onClick:B=>E(h),class:"p-1 text-red-500 hover:text-red-700 transition-colors"},o[7]||(o[7]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,ke)):D("",!0)]))),128)),n.value.length<5?(u(),m("button",{key:0,onClick:F,class:"inline-flex items-center px-3 py-2 text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-all duration-200"},o[8]||(o[8]=[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),I(" 添加颜色 ")]))):D("",!0)])]),e("div",be,[o[10]||(o[10]=e("label",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"预设渐变",-1)),e("div",ye,[(u(!0),m(U,null,T(z.value,g=>(u(),m("button",{key:g.name,onClick:h=>A(g),class:"group relative p-2 border border-gray-200 dark:border-dark-border rounded-lg hover:shadow-md transition-all duration-200"},[e("div",{class:"w-full h-12 rounded",style:S({background:g.style})},null,4),e("div",_e,[e("span",Ce,p(g.name),1)])],8,we))),128))])])])]),_:1},8,["modelValue"])}}}),$e=W(je,[["__scopeId","data-v-b3a71227"]]),Me={class:"space-y-6"},ze={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Le={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Se={class:"flex items-center justify-between"},Ue={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},De={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Fe={class:"flex items-center justify-between"},Re={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Te={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ie={class:"flex items-center justify-between"},Ke={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ne={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Pe={class:"flex items-center justify-between"},Ee={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Ae={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Ge={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},He={class:"flex items-center space-x-3"},qe={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Oe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Je={class:"flex items-center space-x-3"},Qe={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},We={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Xe={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},Ye={class:"p-6"},Ze={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4"},et=["onClick"],tt={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-purple-500 rounded-full flex items-center justify-center z-10"},at={class:"relative"},rt=["src","alt"],ot={class:"absolute top-2 left-2"},lt={class:"absolute bottom-2 left-2"},st={class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-black bg-opacity-50 text-white"},dt={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center"},nt={class:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2"},it=["onClick"],ut=["onClick"],ct={class:"mt-2"},gt=["title"],mt={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},vt={key:0,class:"flex justify-center mt-6"},pt=Q({__name:"index",setup(q){const N=s(),P=s([]),v=s([]),_=s(""),x=s(""),f=s(""),n=s(1),C=s(20),z=s(!1),$=s(!1),F=s(null),E=s(289),A=s(45),G=s(78),L=s(166),c=s([{id:"bg_001",name:"纯白背景",category:"solid",dimensions:"1920x1080",size:"2KB",uploadTime:"2024-01-15 14:30",color:"#ffffff",tags:["白色","纯色","简约"]},{id:"bg_002",name:"蓝色渐变",category:"gradient",dimensions:"1920x1080",size:"5KB",uploadTime:"2024-01-15 14:25",gradientStyle:"linear-gradient(135deg, #667eea 0%, #764ba2 100%)",tags:["蓝色","渐变","现代"]},{id:"bg_003",name:"木纹纹理",thumbnail:"https://picsum.photos/400/300?random=21",url:"https://picsum.photos/1920/1080?random=21",category:"texture",dimensions:"1920x1080",size:"456KB",uploadTime:"2024-01-15 14:20",tags:["木纹","纹理","自然"]},{id:"bg_004",name:"风景背景",thumbnail:"https://picsum.photos/400/300?random=22",url:"https://picsum.photos/1920/1080?random=22",category:"image",dimensions:"1920x1080",size:"1.2MB",uploadTime:"2024-01-15 13:45",tags:["风景","自然","摄影"]},{id:"bg_005",name:"黑色背景",category:"solid",dimensions:"1920x1080",size:"2KB",uploadTime:"2024-01-15 13:40",color:"#000000",tags:["黑色","纯色","经典"]},{id:"bg_006",name:"彩虹渐变",category:"gradient",dimensions:"1920x1080",size:"8KB",uploadTime:"2024-01-15 13:35",gradientStyle:"linear-gradient(90deg, #ff0000, #ff7f00, #ffff00, #00ff00, #0000ff, #4b0082, #9400d3)",tags:["彩虹","渐变","彩色"]}]),o=K(()=>{let a=c.value;if(x.value&&(a=a.filter(t=>t.category===x.value)),f.value&&(f.value==="square"?a=a.filter(t=>t.dimensions.includes("x")&&t.dimensions.split("x")[0]===t.dimensions.split("x")[1]):a=a.filter(t=>t.dimensions===f.value)),_.value){const t=_.value.toLowerCase();a=a.filter(d=>{var l;return d.name.toLowerCase().includes(t)||((l=d.tags)==null?void 0:l.some(k=>k.toLowerCase().includes(t)))})}return a}),y=K(()=>{const a=(n.value-1)*C.value,t=a+C.value;return o.value.slice(a,t)}),M=a=>{if(a.raw){const t=new FileReader;t.onload=d=>{var k,b;const l={id:"bg_"+Date.now(),name:a.name,thumbnail:(k=d.target)==null?void 0:k.result,url:(b=d.target)==null?void 0:b.result,category:"image",dimensions:"1920x1080",size:h(a.size||0),uploadTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),tags:[]};c.value.unshift(l),R.success(`背景 ${a.name} 上传成功`)},t.readAsDataURL(a.raw)}},V=a=>{const t=c.value.findIndex(d=>d.name===a.name);t>-1&&c.value.splice(t,1)},g=a=>{const t={id:"bg_"+Date.now(),name:a.name||"自定义渐变",category:"gradient",dimensions:"1920x1080",size:"5KB",uploadTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),gradientStyle:a.style,tags:["渐变","自定义"]};c.value.unshift(t),R.success("渐变背景创建成功")},h=a=>a<1024?a+" B":a<1024*1024?(a/1024).toFixed(1)+" KB":(a/(1024*1024)).toFixed(1)+" MB",B=a=>v.value.some(t=>t.id===a.id),X=a=>{const t=v.value.findIndex(d=>d.id===a.id);t>-1?v.value.splice(t,1):v.value.push(a)},Y=a=>({solid:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",gradient:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",texture:"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",image:"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300"})[a]||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",Z=a=>({solid:"纯色",gradient:"渐变",texture:"纹理",image:"图片"})[a]||"未知",ee=a=>{F.value=a,z.value=!0},te=a=>{if(a.category==="solid"||a.category==="gradient"){const t=document.createElement("canvas");t.width=1920,t.height=1080;const d=t.getContext("2d");if(d){if(a.category==="solid"&&a.color)d.fillStyle=a.color,d.fillRect(0,0,t.width,t.height);else if(a.category==="gradient"&&a.gradientStyle){const l=d.createLinearGradient(0,0,t.width,t.height);l.addColorStop(0,"#667eea"),l.addColorStop(1,"#764ba2"),d.fillStyle=l,d.fillRect(0,0,t.width,t.height)}t.toBlob(l=>{if(l){const k=URL.createObjectURL(l),b=document.createElement("a");b.href=k,b.download=a.name+".png",document.body.appendChild(b),b.click(),document.body.removeChild(b),URL.revokeObjectURL(k)}})}}else if(a.url){const t=document.createElement("a");t.href=a.url,t.download=a.name,document.body.appendChild(t),t.click(),document.body.removeChild(t)}R.success(`正在下载 ${a.name}`)},ae=()=>{v.value.length!==0&&(v.value.forEach(a=>{const t=c.value.findIndex(d=>d.id===a.id);t>-1&&c.value.splice(t,1)}),R.success(`已删除 ${v.value.length} 个背景`),v.value=[])},re=()=>{n.value=1};return(a,t)=>{const d=w("el-upload"),l=w("el-option"),k=w("el-select"),b=w("el-input"),oe=w("el-pagination");return u(),m(U,null,[e("div",Me,[t[24]||(t[24]=se('<div class="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-purple-100 dark:border-purple-800" data-v-38e7f81f><div class="flex items-center space-x-3" data-v-38e7f81f><div class="w-10 h-10 bg-gradient-to-br from-purple-500 to-indigo-600 rounded-xl flex items-center justify-center" data-v-38e7f81f><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-38e7f81f><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z" data-v-38e7f81f></path></svg></div><div data-v-38e7f81f><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-38e7f81f>背景图库</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-38e7f81f>管理背景图片和纹理素材</p></div></div></div>',1)),e("div",ze,[e("div",Le,[e("div",Se,[e("div",null,[t[8]||(t[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"背景总数",-1)),e("p",Ue,p(E.value),1)]),t[9]||(t[9]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",De,[e("div",Fe,[e("div",null,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"纯色背景",-1)),e("p",Re,p(A.value),1)]),t[11]||(t[11]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5a2 2 0 00-2-2h-4a2 2 0 00-2 2v12a4 4 0 004 4h4a2 2 0 002-2V5z"})])],-1))])]),e("div",Te,[e("div",Ie,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"渐变背景",-1)),e("p",Ke,p(G.value),1)]),t[13]||(t[13]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",Ne,[e("div",Pe,[e("div",null,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"纹理背景",-1)),e("p",Ee,p(L.value),1)]),t[15]||(t[15]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"})])],-1))])])]),e("div",Ae,[e("div",Ge,[e("div",He,[i(d,{ref_key:"uploadRef",ref:N,"file-list":P.value,"on-change":M,"on-remove":V,"auto-upload":!1,accept:"image/*",multiple:"","show-file-list":!1},{default:j(()=>t[16]||(t[16]=[e("button",{class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-purple-500 to-indigo-600 hover:from-purple-600 hover:to-indigo-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),I(" 上传背景 ")],-1)])),_:1,__:[16]},8,["file-list"]),e("button",{onClick:t[0]||(t[0]=r=>$.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},t[17]||(t[17]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),I(" 创建渐变 ")])),v.value.length>0?(u(),m("div",qe,[e("span",Oe," 已选择 "+p(v.value.length)+" 个背景 ",1),e("button",{onClick:ae,class:"inline-flex items-center px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},t[18]||(t[18]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),I(" 批量删除 ")]))])):D("",!0)]),e("div",Je,[i(k,{modelValue:x.value,"onUpdate:modelValue":t[1]||(t[1]=r=>x.value=r),placeholder:"背景类型",style:{width:"120px"},clearable:""},{default:j(()=>[i(l,{label:"全部",value:""}),i(l,{label:"纯色",value:"solid"}),i(l,{label:"渐变",value:"gradient"}),i(l,{label:"纹理",value:"texture"}),i(l,{label:"图片",value:"image"})]),_:1},8,["modelValue"]),i(k,{modelValue:f.value,"onUpdate:modelValue":t[2]||(t[2]=r=>f.value=r),placeholder:"尺寸",style:{width:"120px"},clearable:""},{default:j(()=>[i(l,{label:"全部",value:""}),i(l,{label:"1920x1080",value:"1920x1080"}),i(l,{label:"1280x720",value:"1280x720"}),i(l,{label:"正方形",value:"square"})]),_:1},8,["modelValue"]),i(b,{modelValue:_.value,"onUpdate:modelValue":t[3]||(t[3]=r=>_.value=r),placeholder:"搜索背景...",style:{width:"200px"},clearable:"",onInput:re},{prefix:j(()=>t[19]||(t[19]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"])])])]),e("div",Qe,[e("div",We,[t[20]||(t[20]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"背景素材",-1)),e("p",Xe,"共 "+p(o.value.length)+" 个背景",1)]),e("div",Ye,[e("div",Ze,[(u(!0),m(U,null,T(y.value,r=>(u(),m("div",{key:r.id,class:"group relative cursor-pointer",onClick:O=>X(r)},[B(r)?(u(),m("div",tt,t[21]||(t[21]=[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):D("",!0),e("div",at,[e("div",{class:H(["w-full h-32 rounded-lg border-2 transition-all duration-200 overflow-hidden",B(r)?"border-purple-500":"border-gray-200 dark:border-dark-border group-hover:border-purple-300"])},[r.category==="gradient"?(u(),m("div",{key:0,class:"w-full h-full",style:S({background:r.gradientStyle})},null,4)):r.category==="solid"?(u(),m("div",{key:1,class:"w-full h-full",style:S({backgroundColor:r.color})},null,4)):(u(),m("img",{key:2,src:r.thumbnail,alt:r.name,class:"w-full h-full object-cover"},null,8,rt))],2),e("div",ot,[e("span",{class:H([Y(r.category),"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"])},p(Z(r.category)),3)]),e("div",lt,[e("span",st,p(r.dimensions),1)]),e("div",dt,[e("div",nt,[e("button",{onClick:J(O=>ee(r),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},t[22]||(t[22]=[e("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)]),8,it),e("button",{onClick:J(O=>te(r),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},t[23]||(t[23]=[e("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1)]),8,ut)])])]),e("div",ct,[e("p",{class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate",title:r.name},p(r.name),9,gt),e("p",mt,p(r.size)+" • "+p(r.uploadTime),1)])],8,et))),128))]),o.value.length>C.value?(u(),m("div",vt,[i(oe,{"current-page":n.value,"onUpdate:currentPage":t[4]||(t[4]=r=>n.value=r),"page-size":C.value,"onUpdate:pageSize":t[5]||(t[5]=r=>C.value=r),"page-sizes":[20,40,80],total:o.value.length,layout:"sizes, prev, pager, next",class:"modern-pagination"},null,8,["current-page","page-size","total"])])):D("",!0)])])]),i(de,{modelValue:z.value,"onUpdate:modelValue":t[6]||(t[6]=r=>z.value=r),image:F.value},null,8,["modelValue","image"]),i($e,{modelValue:$.value,"onUpdate:modelValue":t[7]||(t[7]=r=>$.value=r),onCreate:g},null,8,["modelValue"])],64)}}}),ht=W(pt,[["__scopeId","data-v-38e7f81f"]]);export{ht as default};
