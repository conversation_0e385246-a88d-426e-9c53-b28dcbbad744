import{d as J,e as O,r as x,c as v,F as W,g as s,h as b,j as o,a as e,C as $,M as F,k as oe,t as u,E as w,o as m,q,b as K,Q as X,n as Y,A,_ as Z,f as le,p as de,G as ne}from"./index-gGKuRSZs.js";import{_ as ie}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-I6oFGs3s.js";import{r as Q}from"./ArrowDownTrayIcon-DUfu9sNO.js";const ue={class:"space-y-6"},ce={class:"flex justify-start space-x-3"},ge={class:"bg-gray-50 dark:bg-dark-card border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg min-h-[400px] p-6"},xe={key:0},me={class:"grid grid-cols-6 gap-4"},pe={class:"relative"},ke=["src","alt"],ve=["onClick"],fe={class:"mt-1 text-xs text-gray-600 dark:text-dark-text-secondary truncate"},be={key:0,class:"flex justify-center mt-6"},he={key:1,class:"flex flex-col items-center justify-center h-full text-center"},we={class:"flex justify-between items-center"},ye={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},_e={class:"flex space-x-3"},Ce=J({__name:"CreateCropDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(N,{emit:V}){const z=N,M=V,h=O({get:()=>z.modelValue,set:r=>M("update:modelValue",r)}),_=x(!1),c=x([]),g=x([]),y=x(1),C=x(18),S=O(()=>{const r=(y.value-1)*C.value,a=r+C.value;return g.value.slice(r,a)}),D=r=>{const a=new FileReader;a.onload=p=>{var j;const B=Date.now()+Math.random();g.value.push({id:B,name:r.name,url:(j=p.target)==null?void 0:j.result,file:r.raw})},a.readAsDataURL(r.raw)},T=r=>{const a=g.value.findIndex(p=>p.name===r.name);a>-1&&g.value.splice(a,1)},P=r=>{const a=(y.value-1)*C.value+r;g.value.splice(a,1),S.value.length===0&&y.value>1&&y.value--},I=r=>{y.value=r},U=r=>{const a=r.map(p=>({id:p.id||Date.now()+Math.random(),name:p.name,url:p.url,file:p.file}));g.value.push(...a),_.value=!1},L=()=>{if(g.value.length===0){w.warning("请先选择要裁图的图片");return}w.success(`已提交裁图任务，将处理 ${g.value.length} 张图片`),M("success"),h.value=!1,i()},i=()=>{g.value=[],c.value=[],y.value=1};return(r,a)=>{const p=b("el-button"),B=b("el-upload"),j=b("el-pagination"),R=b("el-dialog");return m(),v(W,null,[s(R,{modelValue:h.value,"onUpdate:modelValue":a[3]||(a[3]=k=>h.value=k),title:"新建裁图任务",width:"900px","align-center":"",onClose:i},{footer:o(()=>[e("div",we,[e("div",ye,u(g.value.length>0?`将处理 ${g.value.length} 张图片`:"请先选择图片"),1),e("div",_e,[s(p,{onClick:a[2]||(a[2]=k=>h.value=!1)},{default:o(()=>a[9]||(a[9]=[$("取消")])),_:1,__:[9]}),s(p,{type:"primary",onClick:L,disabled:g.value.length===0},{default:o(()=>a[10]||(a[10]=[$(" 提交任务 ")])),_:1,__:[10]},8,["disabled"])])])]),default:o(()=>[e("div",ue,[e("div",ce,[s(B,{ref:"uploadRef","file-list":c.value,"on-change":D,"on-remove":T,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:o(()=>[s(p,{type:"primary",size:"large"},{default:o(()=>a[5]||(a[5]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),$(" 上传图片 ")])),_:1,__:[5]})]),_:1},8,["file-list"]),s(p,{onClick:a[0]||(a[0]=k=>_.value=!0),size:"large"},{default:o(()=>a[6]||(a[6]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),$(" 图库选择 ")])),_:1,__:[6]})]),e("div",ge,[g.value.length>0?(m(),v("div",xe,[e("div",me,[(m(!0),v(W,null,oe(S.value,(k,l)=>(m(),v("div",{key:k.id||l,class:"relative group"},[e("div",pe,[e("img",{src:k.url,alt:k.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,ke),e("button",{onClick:G=>P(l),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},a[7]||(a[7]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,ve)]),e("div",fe,u(k.name),1)]))),128))]),g.value.length>C.value?(m(),v("div",be,[s(j,{"current-page":y.value,"onUpdate:currentPage":a[1]||(a[1]=k=>y.value=k),"page-size":C.value,total:g.value.length,layout:"prev, pager, next",onCurrentChange:I},null,8,["current-page","page-size","total"])])):F("",!0)])):(m(),v("div",he,a[8]||(a[8]=[e("svg",{class:"w-16 h-16 text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-lg text-gray-500 dark:text-dark-text-secondary mb-2"},"暂无图片",-1),e("p",{class:"text-sm text-gray-400 dark:text-dark-text-secondary"},"请点击上方按钮选择图片",-1)])))])])]),_:1},8,["modelValue"]),s(ie,{modelValue:_.value,"onUpdate:modelValue":a[4]||(a[4]=k=>_.value=k),"theme-color":"yellow",onSelect:U},null,8,["modelValue"])],64)}}}),$e={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},je={class:"flex items-center space-x-3"},Ve={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ze={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Me={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},Te={class:"flex items-center space-x-2"},Be={class:"text-sm font-bold text-green-900 dark:text-green-100"},Se={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},De={class:"flex items-center space-x-2"},Pe={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},Ie={class:"bg-gradient-to-br from-emerald-50 to-emerald-100 dark:from-emerald-900/20 dark:to-emerald-800/20 p-4 rounded-xl border border-emerald-200 dark:border-emerald-800"},Ue={class:"flex items-center space-x-2"},Le={class:"text-sm font-bold text-emerald-900 dark:text-emerald-100"},Re={class:"bg-gradient-to-br from-purple-50 to-purple-100 dark:from-purple-900/20 dark:to-purple-800/20 p-4 rounded-xl border border-purple-200 dark:border-purple-800"},He={class:"flex items-center space-x-2"},Ne={class:"text-sm font-bold text-purple-900 dark:text-purple-100"},Ae={class:"px-6 pb-6"},Oe={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Fe={key:0,class:"flex justify-center"},Ge={key:1,class:"text-gray-400 text-xs"},Ee={key:0,class:"flex justify-center"},We={key:1,class:"text-gray-400 text-xs"},qe={key:0,class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Qe={key:1,class:"text-gray-400 text-xs"},Je=["onClick"],Ke={class:"flex justify-center p-4 border-t border-gray-200 dark:border-dark-border"},Xe={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Ye={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ze={class:"flex items-center space-x-3"},et=J({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(N,{emit:V}){const z=N,M=V,h=O({get:()=>z.modelValue,set:i=>M("update:modelValue",i)}),_=x(!1),c=x(1),g=x(10),y=O(()=>C.value.length),C=x([{fileName:"image1.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/4ade80/ffffff?text=原图1",resultImage:"https://via.placeholder.com/100x100/22c55e/ffffff?text=结果1",downloadUrl:"#"},{fileName:"image2.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/06b6d4/ffffff?text=原图2",resultImage:"https://via.placeholder.com/100x100/0ea5e9/ffffff?text=结果2",downloadUrl:"#"},{fileName:"image3.jpg",status:"failed",thumbnail:"https://via.placeholder.com/100x100/ef4444/ffffff?text=原图3",resultImage:"",errorMessage:"图片格式不支持"},{fileName:"image4.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/8b5cf6/ffffff?text=原图4",resultImage:"https://via.placeholder.com/100x100/7c3aed/ffffff?text=结果4",downloadUrl:"#"},{fileName:"image5.jpg",status:"success",thumbnail:"https://via.placeholder.com/100x100/f59e0b/ffffff?text=原图5",resultImage:"https://via.placeholder.com/100x100/d97706/ffffff?text=结果5",downloadUrl:"#"}]),S=i=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[i]||"未知",D=i=>({success:"成功",failed:"失败"})[i]||"未知",T=()=>{h.value=!1},P=i=>{w.success(`正在下载 ${i.fileName}`)},I=()=>{w.success("正在导出详情...")},U=i=>{g.value=i,c.value=1},L=i=>{c.value=i};return(i,r)=>{const a=b("el-table-column"),p=b("el-image"),B=b("el-table"),j=b("el-pagination"),R=b("el-dialog"),k=X("loading");return m(),q(R,{modelValue:h.value,"onUpdate:modelValue":r[2]||(r[2]=l=>h.value=l),width:"1200px","before-close":T,"show-close":!1,class:"modern-dialog"},{header:o(()=>{var l;return[e("div",$e,[e("div",je,[r[4]||(r[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[r[3]||(r[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"裁图详情",-1)),e("p",Ve,"任务ID: "+u(((l=i.task)==null?void 0:l.id)||""),1)])]),e("button",{onClick:T,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},r[5]||(r[5]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:o(()=>[e("div",Xe,[e("div",Ye," 共 "+u(y.value)+" 条裁图结果 ",1),e("div",Ze,[e("button",{onClick:T,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:I,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(A(Q),{class:"w-5 h-5 mr-2"}),r[15]||(r[15]=$(" 导出详情 "))])])])]),default:o(()=>[i.task?(m(),v("div",ze,[e("div",Me,[e("div",Te,[r[7]||(r[7]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[6]||(r[6]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"任务状态",-1)),e("p",Be,u(S(i.task.status)),1)])])]),e("div",Se,[e("div",De,[r[9]||(r[9]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1)),e("div",null,[r[8]||(r[8]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"目标数量",-1)),e("p",Pe,u(i.task.targetCount),1)])])]),e("div",Ie,[e("div",Ue,[r[11]||(r[11]=e("div",{class:"w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[r[10]||(r[10]=e("p",{class:"text-xs text-emerald-600 dark:text-emerald-400 font-medium"},"成功数量",-1)),e("p",Le,u(i.task.successCount),1)])])]),e("div",Re,[e("div",He,[r[13]||(r[13]=e("div",{class:"w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})])],-1)),e("div",null,[r[12]||(r[12]=e("p",{class:"text-xs text-purple-600 dark:text-purple-400 font-medium"},"操作人",-1)),e("p",Ne,u(i.task.operator),1)])])])])):F("",!0),e("div",Ae,[r[14]||(r[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"裁图结果",-1)),e("div",Oe,[K((m(),q(B,{data:C.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:o(()=>[s(a,{prop:"index",label:"序号",width:"80",align:"center"}),s(a,{label:"预览",width:"100",align:"center"},{default:o(l=>[l.row.status==="success"?(m(),v("div",Fe,[s(p,{src:l.row.thumbnail,"preview-src-list":[l.row.thumbnail],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(m(),v("div",Ge,"无图片"))]),_:1}),s(a,{label:"结果图",width:"100",align:"center"},{default:o(l=>[l.row.status==="success"?(m(),v("div",Ee,[s(p,{src:l.row.resultImage,"preview-src-list":[l.row.resultImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(m(),v("div",We,"无图片"))]),_:1}),s(a,{label:"文件名","min-width":"200"},{default:o(l=>[l.row.fileName?(m(),v("div",qe,u(l.row.fileName),1)):(m(),v("div",Qe,"无文件名"))]),_:1}),s(a,{prop:"status",label:"状态",width:"100",align:"center"},{default:o(l=>[e("span",{class:Y(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",l.row.status==="success"?"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300":"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"])},u(D(l.row.status)),3)]),_:1}),s(a,{label:"操作",width:"120",align:"center"},{default:o(l=>[l.row.status==="success"?(m(),v("button",{key:0,onClick:G=>P(l.row),class:"text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 text-sm font-medium"}," 下载结果 ",8,Je)):F("",!0)]),_:1})]),_:1},8,["data"])),[[k,_.value]]),e("div",Ke,[s(j,{"current-page":c.value,"onUpdate:currentPage":r[0]||(r[0]=l=>c.value=l),"page-size":g.value,"onUpdate:pageSize":r[1]||(r[1]=l=>g.value=l),"page-sizes":[10,20,50],total:y.value,layout:"total, sizes, prev, pager, next",onSizeChange:U,onCurrentChange:L},null,8,["current-page","page-size","total"])])])])]),_:1},8,["modelValue"])}}}),tt=Z(et,[["__scopeId","data-v-f858d7d4"]]),rt={class:"space-y-6"},at={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},st={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ot={class:"flex items-center justify-between"},lt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},dt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},nt={class:"flex items-center justify-between"},it={class:"text-2xl font-bold text-green-600 dark:text-green-400"},ut={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ct={class:"flex items-center justify-between"},gt={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},xt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},mt={class:"flex items-center justify-between"},pt={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},kt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},vt={class:"flex justify-between items-center"},ft={class:"flex items-center space-x-3"},bt={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},ht={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},wt={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},yt={class:"overflow-x-auto"},_t={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Ct={class:"flex items-center space-x-2"},$t={class:"font-medium text-gray-900 dark:text-dark-text"},jt={class:"font-medium text-green-600 dark:text-green-400"},Vt={class:"flex items-center space-x-2"},zt={class:"w-6 h-6 bg-gradient-to-br from-green-400 to-green-500 rounded-full flex items-center justify-center"},Mt={class:"text-white text-xs font-medium"},Tt={class:"text-sm text-gray-900 dark:text-dark-text"},Bt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},St={class:"flex items-center space-x-2"},Dt=["onClick"],Pt={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},It={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ut=J({__name:"index",setup(N){const V=x(!1),z=x(!1),M=x(null),h=x([]),_=x(!1),c=x({currentPage:1,pageSize:20,total:0}),g=x(1248),y=x(94.2),C=x(12),S=x(86),D=x([{id:"CROP001",targetCount:50,successCount:48,status:"completed",operator:"Admin",createTime:"2024-01-15 14:30:25"},{id:"CROP002",targetCount:30,successCount:25,status:"processing",operator:"User1",createTime:"2024-01-15 13:45:12"},{id:"CROP003",targetCount:20,successCount:0,status:"failed",operator:"User2",createTime:"2024-01-15 12:20:08"}]),T=n=>{const t={completed:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",processing:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400",failed:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400",pending:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"};return t[n]||t.pending},P=n=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[n]||"未知",I=n=>{M.value=n,z.value=!0},U=()=>{w.success("正在导出表格数据...")},L=n=>{h.value=n},i=()=>{if(h.value.length===0){w.warning("请先选择要导出的任务");return}w.success(`正在导出 ${h.value.length} 个任务的数据...`)},r=()=>{w.success("数据已刷新")},a=n=>{const{action:t,row:f}=n;switch(t){case"titleGenerate":p(f);break;case"batchListing":B(f);break;case"oneClickCutout":j(f);break;case"superSplit":R(f);break;case"copyrightDetection":k(f);break;default:w.warning("未知操作")}},p=n=>{w.success(`正在为裁图任务 ${n.id} 创建标题生成任务...`)},B=n=>{w.success(`正在为裁图任务 ${n.id} 创建批量刊登任务...`)},j=n=>{w.success(`正在为裁图任务 ${n.id} 创建一键抠图任务...`)},R=n=>{w.success(`正在为裁图任务 ${n.id} 创建超级裂变任务...`)},k=n=>{w.success(`正在为裁图任务 ${n.id} 创建侵权检测任务...`)},l=n=>{c.value.pageSize=n,c.value.currentPage=1,E()},G=n=>{c.value.currentPage=n,E()},E=()=>{_.value=!0,setTimeout(()=>{const n=[{id:"CROP001",targetCount:50,successCount:48,status:"completed",operator:"Admin",createTime:"2024-01-15 14:30:25"},{id:"CROP002",targetCount:30,successCount:25,status:"processing",operator:"User1",createTime:"2024-01-15 13:20:15"},{id:"CROP003",targetCount:75,successCount:70,status:"completed",operator:"User2",createTime:"2024-01-15 12:10:30"},{id:"CROP004",targetCount:20,successCount:0,status:"failed",operator:"User3",createTime:"2024-01-15 11:45:20"},{id:"CROP005",targetCount:60,successCount:55,status:"completed",operator:"Admin",createTime:"2024-01-15 10:30:45"}],t=(c.value.currentPage-1)*c.value.pageSize,f=t+c.value.pageSize;D.value=n.slice(t,f),c.value.total=n.length,_.value=!1},500)};return le(()=>{E()}),(n,t)=>{const f=b("el-table-column"),H=b("el-dropdown-item"),ee=b("el-dropdown-menu"),te=b("el-dropdown"),re=b("el-table"),ae=b("el-pagination"),se=X("loading");return m(),v(W,null,[e("div",rt,[t[26]||(t[26]=de('<div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-100 dark:border-green-800" data-v-e45cb2d4><div class="flex items-center space-x-3" data-v-e45cb2d4><div class="w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center" data-v-e45cb2d4><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-e45cb2d4><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4" data-v-e45cb2d4></path></svg></div><div data-v-e45cb2d4><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-e45cb2d4>智能裁图</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-e45cb2d4>AI智能图片裁剪和优化工具</p></div></div></div>',1)),e("div",at,[e("div",st,[e("div",ot,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总裁图数",-1)),e("p",lt,u(g.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",dt,[e("div",nt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",it,u(y.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-emerald-100 dark:bg-emerald-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-emerald-600 dark:text-emerald-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ut,[e("div",ct,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",gt,u(C.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",xt,[e("div",mt,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日裁图",-1)),e("p",pt,u(S.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])])]),e("div",kt,[e("div",vt,[e("div",ft,[e("button",{onClick:t[0]||(t[0]=d=>V.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(A(ne),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=$(" 新建裁图 "))]),e("button",{onClick:U,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[s(A(Q),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=$(" 导出表格 "))]),h.value.length>0?(m(),v("div",bt,[e("span",ht," 已选择 "+u(h.value.length)+" 项 ",1),e("button",{onClick:i,class:"inline-flex items-center px-3 py-1.5 bg-green-500 hover:bg-green-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[s(A(Q),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=$(" 批量导出 "))])])):F("",!0)])])]),e("div",wt,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"裁图任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有裁图任务")],-1)),e("div",yt,[K((m(),q(re,{data:D.value,style:{width:"100%"},onSelectionChange:L,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:o(()=>[s(f,{type:"selection",width:"55"}),s(f,{prop:"id",label:"裁图ID",width:"120"},{default:o(d=>[e("span",_t,u(d.row.id),1)]),_:1}),s(f,{label:"裁图数量",width:"150"},{default:o(d=>[e("div",Ct,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"目标:",-1)),e("span",$t,u(d.row.targetCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功:",-1)),e("span",jt,u(d.row.successCount),1)])]),_:1}),s(f,{prop:"status",label:"裁切状态",width:"120"},{default:o(d=>[e("span",{class:Y([T(d.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},u(P(d.row.status)),3)]),_:1}),s(f,{prop:"operator",label:"操作人",width:"100"},{default:o(d=>[e("div",Vt,[e("div",zt,[e("span",Mt,u(d.row.operator.charAt(0)),1)]),e("span",Tt,u(d.row.operator),1)])]),_:1}),s(f,{prop:"createTime",label:"创建时间",width:"180"},{default:o(d=>[e("div",Bt,u(d.row.createTime),1)]),_:1}),s(f,{label:"操作",width:"180"},{default:o(d=>[e("div",St,[e("button",{onClick:Lt=>I(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-green-600 dark:text-green-400 hover:text-green-700 dark:hover:text-green-300 bg-green-50 dark:bg-green-900/20 hover:bg-green-100 dark:hover:bg-green-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Dt),s(te,{onCommand:a,trigger:"click"},{dropdown:o(()=>[s(ee,null,{default:o(()=>[s(H,{command:{action:"titleGenerate",row:d.row}},{default:o(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[19]},1032,["command"]),s(H,{command:{action:"batchListing",row:d.row}},{default:o(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[20]},1032,["command"]),s(H,{command:{action:"oneClickCutout",row:d.row}},{default:o(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[21]},1032,["command"]),s(H,{command:{action:"superSplit",row:d.row}},{default:o(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"]),s(H,{command:{action:"copyrightDetection",row:d.row}},{default:o(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:o(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[$(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[se,_.value]])]),e("div",Pt,[e("div",It," 共 "+u(c.value.total)+" 条记录 ",1),s(ae,{"current-page":c.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>c.value.currentPage=d),"page-size":c.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>c.value.pageSize=d),"page-sizes":[10,20,50,100],total:c.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:l,onCurrentChange:G,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),s(Ce,{modelValue:V.value,"onUpdate:modelValue":t[3]||(t[3]=d=>V.value=d),onSuccess:r},null,8,["modelValue"]),s(tt,{modelValue:z.value,"onUpdate:modelValue":t[4]||(t[4]=d=>z.value=d),task:M.value},null,8,["modelValue","task"])],64)}}}),At=Z(Ut,[["__scopeId","data-v-e45cb2d4"]]);export{At as default};
