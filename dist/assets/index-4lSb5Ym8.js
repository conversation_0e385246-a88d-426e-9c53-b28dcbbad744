import{c as d,a as e,o,r as S,e as W,d as le,q as H,h as _,j as i,M as A,g as a,A as z,k as Z,t as s,C as w,F as P,s as Q,y as U,S as ue,D as ce,_ as ne,K as Le,b as ae,v as ye,n as q,w as Me,T as We,H as Te,J as Re,E as T,f as Ae,p as he,R as Ue,P as Se,U as Pe,Q as He}from"./index-D_iwS02E.js";import{r as _e}from"./MagnifyingGlassIcon-CrHoZUMX.js";import{r as pe}from"./DocumentTextIcon-Bq4b314d.js";function De(B,j){return o(),d("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"})])}function $e(B,j){return o(),d("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 5.653c0-.856.917-1.398 1.667-.986l11.54 6.347a1.125 1.125 0 0 1 0 1.972l-11.54 6.347a1.125 1.125 0 0 1-1.667-.986V5.653Z"})])}function ge(B,j){return o(),d("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m7.848 8.25 1.536.887M7.848 8.25a3 3 0 1 1-5.196-3 3 3 0 0 1 5.196 3Zm1.536.887a2.165 2.165 0 0 1 1.083 1.839c.005.351.054.695.14 1.024M9.384 9.137l2.077 1.199M7.848 15.75l1.536-.887m-1.536.887a3 3 0 1 1-5.196 3 3 3 0 0 1 5.196-3Zm1.536-.887a2.165 2.165 0 0 0 1.083-1.838c.005-.352.054-.695.14-1.025m-1.223 2.863 2.077-1.199m0-3.328a4.323 4.323 0 0 1 2.068-1.379l5.325-1.628a4.5 4.5 0 0 1 2.48-.044l.803.215-7.794 4.5m-2.882-1.664A4.33 4.33 0 0 0 10.607 12m3.736 0 7.794 4.5-.802.215a4.5 4.5 0 0 1-2.48-.043l-5.326-1.629a4.324 4.324 0 0 1-2.068-1.379M14.343 12l-2.882 1.664"})])}function xe(B,j){return o(),d("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"})])}function Ce(B,j){return o(),d("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M5.25 7.5A2.25 2.25 0 0 1 7.5 5.25h9a2.25 2.25 0 0 1 2.25 2.25v9a2.25 2.25 0 0 1-2.25 2.25h-9a2.25 2.25 0 0 1-2.25-2.25v-9Z"})])}const oe=S([]),we=S([]),se=S(!1),Y=[{id:"WF001",name:"商品采集+智能裁图+批量刊登",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:156,status:"enabled",creator:"张三",createTime:"2024-01-15 10:30:00"},{id:"WF002",name:"一键抠图+超级裂变",apps:[{id:"app1",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"manual",timeout:25,onError:"retry"}},{id:"app2",name:"超级裂变",type:"super-split",settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"}}],usageCount:89,status:"enabled",creator:"李四",createTime:"2024-01-14 14:20:00"},{id:"WF003",name:"标题生成+POD合成+批量刊登",apps:[{id:"app1",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"manual",timeout:15,onError:"stop"}},{id:"app2",name:"POD合成",type:"pod-compose",settings:{mode:"auto",productSelection:"previous",timeout:45,onError:"retry"}},{id:"app3",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:234,status:"disabled",creator:"王五",createTime:"2024-01-13 09:15:00"},{id:"WF004",name:"智能裁图+一键抠图",apps:[{id:"app1",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"manual",timeout:20,onError:"skip"}},{id:"app2",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}}],usageCount:67,status:"enabled",creator:"赵六",createTime:"2024-01-12 16:45:00"},{id:"WF005",name:"完整电商流程",apps:[{id:"app1",name:"商品采集",type:"product-collection",settings:{mode:"auto",productSelection:"manual",timeout:30,onError:"stop"}},{id:"app2",name:"智能裁图",type:"smart-crop",settings:{mode:"auto",productSelection:"previous",timeout:20,onError:"skip"}},{id:"app3",name:"一键抠图",type:"one-click-cutout",settings:{mode:"auto",productSelection:"previous",timeout:25,onError:"retry"}},{id:"app4",name:"标题生成",type:"title-generator",settings:{mode:"manual",productSelection:"previous",timeout:15,onError:"stop"}},{id:"app5",name:"批量刊登",type:"batch-listing",settings:{mode:"manual",productSelection:"previous",timeout:60,onError:"stop"}}],usageCount:423,status:"enabled",creator:"孙七",createTime:"2024-01-11 11:20:00"}],Fe=()=>{oe.value=[...Y]},Oe=()=>oe.value,Ze=async B=>{se.value=!0;try{await new Promise(N=>setTimeout(N,1e3));const j={id:`WF${String(oe.value.length+1).padStart(3,"0")}`,name:B.name,apps:B.apps,usageCount:0,status:"enabled",creator:"当前用户",createTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit",second:"2-digit"})};return oe.value.unshift(j),j}finally{se.value=!1}},Xe=[{id:"EXE001",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:Y[0],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:35:00",duration:"5分钟",inputCount:0,outputCount:50},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-15 10:35:00",endTime:"2024-01-15 10:40:00",duration:"5分钟",inputCount:50,outputCount:50},{appId:"app3",appName:"批量刊登",status:"completed",startTime:"2024-01-15 10:40:00",endTime:"2024-01-15 10:45:00",duration:"5分钟",inputCount:50,outputCount:48}],startTime:"2024-01-15 10:30:00",endTime:"2024-01-15 10:45:00",duration:"15分钟",executor:"张三"},{id:"EXE002",workflowId:"WF002",workflowName:"一键抠图+超级裂变",workflow:Y[1],status:"running",stepResults:[{appId:"app1",appName:"一键抠图",status:"completed",startTime:"2024-01-15 14:20:00",endTime:"2024-01-15 14:25:00",duration:"5分钟",inputCount:30,outputCount:30},{appId:"app2",appName:"超级裂变",status:"running",startTime:"2024-01-15 14:25:00",inputCount:30,outputCount:0}],startTime:"2024-01-15 14:20:00",duration:"8分钟",executor:"李四"},{id:"EXE003",workflowId:"WF001",workflowName:"商品采集+智能裁图+批量刊登",workflow:Y[0],status:"failed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:05:00",duration:"5分钟",inputCount:0,outputCount:25},{appId:"app2",appName:"智能裁图",status:"failed",startTime:"2024-01-15 09:05:00",endTime:"2024-01-15 09:07:00",duration:"2分钟",inputCount:25,outputCount:0,errorMessage:"图片格式不支持"}],startTime:"2024-01-15 09:00:00",endTime:"2024-01-15 09:07:00",duration:"7分钟",executor:"王五"},{id:"EXE004",workflowId:"WF005",workflowName:"完整电商流程",workflow:Y[4],status:"completed",stepResults:[{appId:"app1",appName:"商品采集",status:"completed",startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:10:00",duration:"10分钟",inputCount:0,outputCount:100},{appId:"app2",appName:"智能裁图",status:"completed",startTime:"2024-01-14 16:10:00",endTime:"2024-01-14 16:20:00",duration:"10分钟",inputCount:100,outputCount:100},{appId:"app3",appName:"一键抠图",status:"completed",startTime:"2024-01-14 16:20:00",endTime:"2024-01-14 16:30:00",duration:"10分钟",inputCount:100,outputCount:95},{appId:"app4",appName:"标题生成",status:"completed",startTime:"2024-01-14 16:30:00",endTime:"2024-01-14 16:35:00",duration:"5分钟",inputCount:95,outputCount:95},{appId:"app5",appName:"批量刊登",status:"completed",startTime:"2024-01-14 16:35:00",endTime:"2024-01-14 16:45:00",duration:"10分钟",inputCount:95,outputCount:92}],startTime:"2024-01-14 16:00:00",endTime:"2024-01-14 16:45:00",duration:"45分钟",executor:"孙七"},{id:"EXE005",workflowId:"WF003",workflowName:"标题生成+POD合成+批量刊登",workflow:Y[2],status:"pending",stepResults:[{appId:"app1",appName:"标题生成",status:"pending"},{appId:"app2",appName:"POD合成",status:"pending"},{appId:"app3",appName:"批量刊登",status:"pending"}],startTime:"2024-01-15 15:00:00",executor:"赵六"}],qe=async()=>{se.value=!0;try{return await new Promise(B=>setTimeout(B,500)),we.value=[...Xe],we.value}finally{se.value=!1}};W(()=>oe.value),W(()=>we.value),W(()=>se.value);const Ke={class:"mb-4"},Je={class:"grid grid-cols-1 md:grid-cols-2 gap-6 max-h-[500px] overflow-y-auto"},Qe=["onClick"],Ge={class:"flex justify-between items-start mb-3"},Ye={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},et={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},tt={class:"mb-3"},rt={class:"flex items-center space-x-2 overflow-x-auto pb-2"},at={class:"flex items-center space-x-1 flex-shrink-0"},ot={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},st={class:"flex items-center space-x-1 bg-blue-100 dark:bg-blue-900/30 rounded px-2 py-1 flex-shrink-0"},lt={class:"text-xs text-blue-700 dark:text-blue-300"},nt={class:"flex items-center space-x-1 flex-shrink-0"},dt={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},it={class:"flex justify-between items-center text-sm text-gray-500 dark:text-dark-text-secondary"},ut={key:0,class:"text-center py-12"},ct={class:"flex justify-end space-x-3"},pt=le({__name:"WorkflowTemplateDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select","create-blank"],setup(B,{emit:j}){const N=B,D=j,E=W({get:()=>N.modelValue,set:L=>D("update:modelValue",L)}),M=S(""),V=W(()=>Oe()),p=W(()=>M.value?V.value.filter(L=>L.name.toLowerCase().includes(M.value.toLowerCase())||L.apps.some(y=>y.name.toLowerCase().includes(M.value.toLowerCase()))):V.value),f=L=>({"product-collection":ce,"smart-crop":ge,"one-click-cutout":xe,"super-split":U,"title-generator":pe,"batch-listing":ue,"pod-compose":U})[L]||U,I=L=>{D("select",L),x()},R=()=>{D("create-blank"),x()},x=()=>{M.value="",E.value=!1};return(L,y)=>{const m=_("el-input"),c=_("el-tag"),v=_("el-button"),n=_("el-dialog");return o(),H(n,{modelValue:E.value,"onUpdate:modelValue":y[1]||(y[1]=b=>E.value=b),title:"工作流模板",width:"1000px","close-on-click-modal":!1,class:"template-dialog"},{footer:i(()=>[e("div",ct,[a(v,{onClick:x,size:"large"},{default:i(()=>y[5]||(y[5]=[w(" 取消 ")])),_:1,__:[5]}),a(v,{onClick:R,type:"primary",size:"large",plain:""},{default:i(()=>y[6]||(y[6]=[w(" 创建空白工作流 ")])),_:1,__:[6]})])]),default:i(()=>[e("div",Ke,[a(m,{modelValue:M.value,"onUpdate:modelValue":y[0]||(y[0]=b=>M.value=b),placeholder:"搜索模板...",size:"large",clearable:""},{prefix:i(()=>[a(z(_e),{class:"w-5 h-5 text-gray-400"})]),_:1},8,["modelValue"])]),e("div",Je,[(o(!0),d(P,null,Z(p.value,b=>(o(),d("div",{key:b.id,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4 border border-gray-200 dark:border-dark-border hover:border-blue-300 dark:hover:border-blue-600 cursor-pointer transition-all duration-200",onClick:g=>I(b)},[e("div",Ge,[e("div",null,[e("h3",Ye,s(b.name),1),e("p",et,s(b.apps.length)+" 个应用 · 使用 "+s(b.usageCount)+" 次 ",1)]),a(c,{type:b.status==="enabled"?"success":"danger",size:"small"},{default:i(()=>[w(s(b.status==="enabled"?"可用":"禁用"),1)]),_:2},1032,["type"])]),e("div",tt,[e("div",rt,[e("div",at,[e("div",ot,[a(z($e),{class:"w-3 h-3 text-white"})]),y[2]||(y[2]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),(o(!0),d(P,null,Z(b.apps,(g,F)=>(o(),d(P,{key:F},[a(z(De),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",st,[(o(),H(Q(f(g.type)),{class:"w-3 h-3 text-blue-600 dark:text-blue-400"})),e("span",lt,s(g.name),1)])],64))),128)),a(z(De),{class:"w-3 h-3 text-gray-400 flex-shrink-0"}),e("div",nt,[e("div",dt,[a(z(Ce),{class:"w-3 h-3 text-white"})]),y[3]||(y[3]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])]),e("div",it,[e("span",null,"创建者："+s(b.creator),1),e("span",null,s(b.createTime),1)])],8,Qe))),128))]),p.value.length===0?(o(),d("div",ut,[a(z(U),{class:"w-16 h-16 mx-auto text-gray-400 mb-4"}),y[4]||(y[4]=e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"没有找到匹配的模板",-1))])):A("",!0)]),_:1},8,["modelValue"])}}}),gt=ne(pt,[["__scopeId","data-v-56b174c9"]]),xt={class:"flex items-center justify-between w-full"},mt={class:"flex items-center space-x-2"},kt={class:"flex h-[700px] -mx-6 -mt-4"},bt={class:"w-72 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-card border-r border-gray-200 dark:border-dark-border p-6"},vt={class:"mb-6"},ft={class:"flex items-center justify-between mb-4"},ht={class:"text-xs text-gray-500 dark:text-dark-text-secondary bg-white dark:bg-dark-surface px-2 py-1 rounded-full"},yt={class:"relative"},wt={class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},_t={class:"space-y-3 max-h-[580px] overflow-y-auto custom-scrollbar"},$t=["onDragstart"],Ct={class:"w-12 h-12 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-xl flex items-center justify-center mr-4 group-hover:scale-110 transition-transform duration-300"},Vt={class:"flex-1 min-w-0"},jt={class:"text-sm font-semibold text-gray-900 dark:text-dark-text truncate mb-1"},Mt={class:"text-xs text-gray-500 dark:text-dark-text-secondary truncate"},Tt={key:0,class:"text-center py-12"},St={class:"flex-1 px-6 bg-white dark:bg-dark-card"},Dt={class:"mb-6 pt-6"},zt={class:"flex items-center justify-between mb-4"},Et={class:"flex items-center space-x-2"},It={class:"text-xs text-gray-500 dark:text-dark-text-secondary bg-gray-100 dark:bg-dark-surface px-2 py-1 rounded-full"},Bt={class:"relative"},Nt={class:"relative z-10 flex flex-col items-center space-y-8"},Lt={class:"flex flex-col items-center space-y-3"},Wt={class:"w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-2xl flex items-center justify-center shadow-xl transform hover:scale-105 transition-transform duration-300"},Rt={key:0,class:"flex flex-col items-center space-y-8 w-full"},At={class:"w-full max-w-4xl"},Ut=["onClick"],Pt={class:"flex flex-col items-center space-y-3 p-4"},Ht={class:"relative"},Ft={class:"w-16 h-16 bg-gradient-to-br from-blue-400 to-purple-600 rounded-2xl flex items-center justify-center shadow-xl"},Ot={class:"absolute -top-2 -left-2 w-6 h-6 bg-gradient-to-br from-orange-400 to-red-500 rounded-full flex items-center justify-center text-xs font-bold text-white shadow-lg"},Zt=["onClick"],Xt=["onClick"],qt={class:"text-center"},Kt={class:"text-xs font-semibold text-gray-700 dark:text-dark-text bg-white dark:bg-dark-surface px-2 py-1 rounded-full shadow-md max-w-20 truncate block"},Jt={class:"flex flex-col items-center space-y-3"},Qt={class:"w-20 h-20 bg-gradient-to-br from-red-400 to-red-600 rounded-2xl flex items-center justify-center shadow-xl transform hover:scale-105 transition-transform duration-300"},Gt={key:0,class:"absolute inset-0 flex items-center justify-center z-20"},Yt={class:"text-center text-gray-500 dark:text-dark-text-secondary"},er={class:"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-2xl flex items-center justify-center"},tr={class:"w-96 bg-gradient-to-b from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-card border-l border-gray-200 dark:border-dark-border p-6"},rr={key:0,class:"space-y-6"},ar={class:"bg-white dark:bg-dark-surface rounded-2xl p-6 shadow-lg border border-gray-200 dark:border-dark-border"},or={class:"flex items-center space-x-4 mb-6"},sr={class:"w-14 h-14 bg-gradient-to-br from-blue-100 to-blue-200 dark:from-blue-900/30 dark:to-blue-800/30 rounded-2xl flex items-center justify-center"},lr={class:"flex-1"},nr={class:"font-bold text-gray-900 dark:text-dark-text text-lg"},dr={class:"text-sm text-gray-500 dark:text-dark-text-secondary mt-1"},ir={class:"flex items-center mt-2"},ur={class:"text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-1 rounded-full font-medium"},cr={class:"space-y-4"},pr={key:1,class:"text-center text-gray-500 dark:text-dark-text-secondary py-8"},gr={class:"flex justify-end space-x-3"},xr=le({__name:"CreateWorkflowDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(B,{emit:j}){const N=B,D=j,E=W({get:()=>N.modelValue,set:k=>D("update:modelValue",k)}),M=S(!1),V=S(""),p=S(null),f=S(!1),I=S(!1),R=Le({name:""}),x=S([]),L=[{id:"product-collection",name:"商品采集",type:"product-collection",description:"采集电商平台商品信息"},{id:"smart-crop",name:"智能裁图",type:"smart-crop",description:"智能裁剪商品图片"},{id:"one-click-cutout",name:"一键抠图",type:"one-click-cutout",description:"自动抠图去背景"},{id:"super-split",name:"超级裂变",type:"super-split",description:"图片批量裂变处理"},{id:"title-generator",name:"标题生成",type:"title-generator",description:"智能生成商品标题"},{id:"batch-listing",name:"批量刊登",type:"batch-listing",description:"批量刊登商品到平台"},{id:"pod-compose",name:"POD合成",type:"pod-compose",description:"POD商品合成处理"}],y=W(()=>L.filter(k=>k.name.toLowerCase().includes(V.value.toLowerCase())||k.description.toLowerCase().includes(V.value.toLowerCase()))),m=W(()=>R.name.trim()&&x.value.length>0),c=k=>({"product-collection":ce,"smart-crop":ge,"one-click-cutout":xe,"super-split":U,"title-generator":pe,"batch-listing":ue,"pod-compose":U})[k]||U,v=()=>{T.info("跳转到应用市场功能开发中...")},n=k=>{R.name=`${k.name} - 副本`,x.value=k.apps.map(r=>{const O=L.find(G=>G.type===r.type);return{id:`app_${Date.now()}_${Math.random().toString(36).substring(2,9)}`,name:r.name,type:r.type,description:(O==null?void 0:O.description)||"",settings:{...r.settings},datasetConfig:{}}}),f.value=!1,T.success(`已从模板"${k.name}"加载配置`)},b=(k,r)=>{k.dataTransfer&&(k.dataTransfer.setData("application/json",JSON.stringify(r)),k.dataTransfer.effectAllowed="copy")},g=k=>{k.preventDefault(),I.value=!0,k.dataTransfer&&(k.dataTransfer.dropEffect="copy")},F=k=>{k.preventDefault(),I.value=!1},K=k=>{if(k.preventDefault(),I.value=!1,k.dataTransfer)try{const r=JSON.parse(k.dataTransfer.getData("application/json"));ee(r),T.success(`已添加应用：${r.name}`)}catch(r){console.error("Failed to parse dropped data:",r),T.error("添加应用失败")}},ee=k=>{const r={...k,id:`${k.id}_${Date.now()}`,settings:{mode:"auto",productSelection:"previous",timeout:30,onError:"stop"},datasetConfig:{}};x.value.push(r),p.value=x.value.length-1},$=k=>{x.value.splice(k,1),p.value===k?p.value=null:p.value!==null&&p.value>k&&p.value--},C=()=>{p.value=null},me=()=>{x.value.length>0&&(x.value=[],p.value=null,T.success("工作流已清空"))},ke=async()=>{if(m.value){M.value=!0;try{await Ze({name:R.name,apps:x.value}),T.success("工作流创建成功！"),D("success"),de()}catch{T.error("创建失败，请重试")}finally{M.value=!1}}},de=()=>{R.name="",x.value=[],p.value=null,V.value="",f.value=!1,E.value=!1};return(k,r)=>{const O=_("el-button"),G=_("el-radio"),ie=_("el-radio-group"),X=_("el-option"),te=_("el-select"),be=_("el-input-number"),ve=_("el-dialog");return o(),H(ve,{modelValue:E.value,"onUpdate:modelValue":r[10]||(r[10]=h=>E.value=h),width:"1400px","close-on-click-modal":!1,"close-on-press-escape":!1,class:"workflow-dialog","align-center":""},{header:i(()=>[e("div",xt,[r[13]||(r[13]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])]),e("div",null,[e("h2",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"新建工作流"),e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"拖拽应用构建自动化工作流程")])],-1)),e("div",mt,[a(O,{onClick:r[0]||(r[0]=h=>f.value=!0),size:"small",type:"info",plain:"",class:"transform hover:scale-105 transition-transform duration-200"},{default:i(()=>r[11]||(r[11]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),w(" 选择模板 ")])),_:1,__:[11]}),a(O,{onClick:v,type:"primary",size:"small",plain:"",class:"transform hover:scale-105 transition-transform duration-200"},{default:i(()=>r[12]||(r[12]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})],-1),w(" 应用市场 ")])),_:1,__:[12]})])])]),footer:i(()=>[e("div",gr,[a(O,{onClick:de,size:"large"},{default:i(()=>r[36]||(r[36]=[w("取消")])),_:1,__:[36]}),a(O,{onClick:ke,type:"primary",size:"large",loading:M.value,disabled:!m.value},{default:i(()=>[w(s(M.value?"创建中...":"确定创建"),1)]),_:1},8,["loading","disabled"])])]),default:i(()=>[e("div",kt,[e("div",bt,[e("div",vt,[e("div",ft,[r[14]||(r[14]=e("h3",{class:"text-lg font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),w(" 可用应用 ")],-1)),e("div",ht,s(y.value.length)+" 个 ",1)]),e("div",yt,[e("div",wt,[a(z(_e),{class:"w-4 h-4 text-gray-400"})]),ae(e("input",{"onUpdate:modelValue":r[1]||(r[1]=h=>V.value=h),placeholder:"搜索应用...",class:"w-full pl-10 pr-4 py-2.5 bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-sm"},null,512),[[ye,V.value]])])]),e("div",_t,[(o(!0),d(P,null,Z(y.value,h=>(o(),d("div",{key:h.id,class:"group relative flex items-center p-4 bg-white dark:bg-dark-surface rounded-xl cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 transition-all duration-300 transform hover:scale-105 hover:shadow-lg border border-gray-100 dark:border-dark-border hover:border-blue-200 dark:hover:border-blue-700",draggable:"true",onDragstart:l=>b(l,h)},[e("div",Ct,[(o(),H(Q(c(h.type)),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"}))]),e("div",Vt,[e("div",jt,s(h.name),1),e("div",Mt,s(h.description),1)]),r[15]||(r[15]=e("div",{class:"flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300"},[e("div",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"拖拽"),e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})])],-1)),r[16]||(r[16]=e("div",{class:"absolute left-0 top-0 bottom-0 w-1 bg-blue-500 rounded-r-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"},null,-1))],40,$t))),128)),y.value.length===0?(o(),d("div",Tt,r[17]||(r[17]=[e("svg",{class:"w-12 h-12 mx-auto text-gray-400 mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"未找到匹配的应用",-1)]))):A("",!0)])]),e("div",St,[e("div",Dt,[e("div",zt,[r[18]||(r[18]=e("h3",{class:"text-lg font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})]),w(" 工作流设计 ")],-1)),e("div",Et,[e("div",It,s(x.value.length)+" 个应用 ",1),e("button",{onClick:me,class:"text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 px-2 py-1 rounded-full transition-all duration-200"}," 清空 ")])]),e("div",Bt,[ae(e("input",{"onUpdate:modelValue":r[2]||(r[2]=h=>R.name=h),placeholder:"请输入工作流名称...",class:"w-full px-4 py-3 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 text-gray-900 dark:text-dark-text placeholder-gray-500"},null,512),[[ye,R.name]]),r[19]||(r[19]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center"},[e("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})])],-1))])]),e("div",{class:q(["bg-gradient-to-br from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-border rounded-2xl p-8 min-h-[580px] border-2 border-dashed border-gray-300 dark:border-dark-border relative overflow-hidden transition-all duration-300",{"border-blue-400 bg-blue-50 dark:bg-blue-900/10 scale-105":I.value}]),onDrop:K,onDragover:g,onDragleave:F},[r[27]||(r[27]=e("div",{class:"absolute inset-0 opacity-5"},[e("div",{class:"absolute top-10 left-10 w-20 h-20 bg-blue-500 rounded-full"}),e("div",{class:"absolute top-32 right-16 w-16 h-16 bg-purple-500 rounded-full"}),e("div",{class:"absolute bottom-20 left-20 w-12 h-12 bg-green-500 rounded-full"}),e("div",{class:"absolute bottom-32 right-32 w-24 h-24 bg-orange-500 rounded-full"})],-1)),e("div",Nt,[e("div",Lt,[e("div",Wt,[a(z($e),{class:"w-10 h-10 text-white"})]),r[20]||(r[20]=e("span",{class:"text-sm font-bold text-gray-700 dark:text-dark-text bg-white dark:bg-dark-surface px-3 py-1 rounded-full shadow-md"},"开始",-1))]),x.value.length>0?(o(),d("div",Rt,[r[21]||(r[21]=e("div",{class:"flex flex-col items-center"},[e("div",{class:"w-1 h-12 bg-gradient-to-b from-green-400 to-blue-400 rounded-full"}),e("div",{class:"w-3 h-3 bg-blue-400 rounded-full animate-pulse"})],-1)),e("div",At,[a(z(Re),{modelValue:x.value,"onUpdate:modelValue":r[3]||(r[3]=h=>x.value=h),onEnd:C,"item-key":"id",animation:300,handle:".drag-handle","ghost-class":"sortable-ghost","chosen-class":"sortable-chosen","drag-class":"sortable-drag",class:"grid grid-cols-5 gap-6 justify-items-center"},{item:i(({element:h,index:l})=>[e("div",{class:q(["relative group cursor-pointer drag-handle transform transition-all duration-300 hover:scale-110",{"ring-4 ring-purple-400 ring-opacity-50 scale-110":p.value===l,"hover:shadow-2xl":p.value!==l}]),onClick:t=>p.value=l},[e("div",Pt,[e("div",Ht,[e("div",Ft,[(o(),H(Q(c(h.type)),{class:"w-8 h-8 text-white"}))]),e("div",Ot,s(l+1),1),e("button",{onClick:Me(t=>$(l),["stop"]),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg transform hover:scale-110"},[a(z(We),{class:"w-3 h-3 text-white"})],8,Zt),e("button",{onClick:Me(t=>p.value=l,["stop"]),class:"absolute -bottom-2 -right-2 w-6 h-6 bg-blue-500 hover:bg-blue-600 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-all duration-200 shadow-lg transform hover:scale-110"},[a(z(Te),{class:"w-3 h-3 text-white"})],8,Xt)]),e("div",qt,[e("span",Kt,s(h.name),1)])])],10,Ut)]),_:1},8,["modelValue"])]),r[22]||(r[22]=e("div",{class:"flex flex-col items-center"},[e("div",{class:"w-3 h-3 bg-purple-400 rounded-full animate-pulse"}),e("div",{class:"w-1 h-12 bg-gradient-to-b from-purple-400 to-red-400 rounded-full"})],-1))])):A("",!0),e("div",Jt,[e("div",Qt,[a(z(Ce),{class:"w-10 h-10 text-white"})]),r[23]||(r[23]=e("span",{class:"text-sm font-bold text-gray-700 dark:text-dark-text bg-white dark:bg-dark-surface px-3 py-1 rounded-full shadow-md"},"结束",-1))])]),x.value.length===0?(o(),d("div",Gt,[e("div",Yt,[e("div",er,[a(z(U),{class:"w-12 h-12 text-blue-400 dark:text-blue-500"})]),r[24]||(r[24]=e("h4",{class:"text-lg font-semibold text-gray-700 dark:text-dark-text mb-2"},"开始构建工作流",-1)),r[25]||(r[25]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary mb-4"},"拖拽左侧应用到此处开始构建工作流",-1)),r[26]||(r[26]=e("div",{class:"flex items-center justify-center space-x-2 text-xs text-gray-400 dark:text-dark-text-secondary"},[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16l-4-4m0 0l4-4m-4 4h18"})]),e("span",null,"拖拽应用"),e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]),e("span",null,"配置设置"),e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})]),e("span",null,"运行工作流")],-1))])])):A("",!0)],34)]),e("div",tr,[r[35]||(r[35]=e("div",{class:"mb-6"},[e("h3",{class:"text-lg font-bold text-gray-900 dark:text-dark-text flex items-center"},[e("svg",{class:"w-5 h-5 mr-2 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]),w(" 应用设置 ")])],-1)),p.value!==null&&x.value[p.value]?(o(),d("div",rr,[e("div",ar,[e("div",or,[e("div",sr,[(o(),H(Q(c(x.value[p.value].type)),{class:"w-7 h-7 text-blue-600 dark:text-blue-400"}))]),e("div",lr,[e("div",nr,s(x.value[p.value].name),1),e("div",dr,s(x.value[p.value].description),1),e("div",ir,[e("span",ur," 步骤 "+s(p.value+1),1)])])]),e("div",cr,[e("div",null,[r[30]||(r[30]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"执行模式",-1)),a(ie,{modelValue:x.value[p.value].settings.mode,"onUpdate:modelValue":r[4]||(r[4]=h=>x.value[p.value].settings.mode=h),size:"small"},{default:i(()=>[a(G,{label:"auto"},{default:i(()=>r[28]||(r[28]=[w("自动执行")])),_:1,__:[28]}),a(G,{label:"manual"},{default:i(()=>r[29]||(r[29]=[w("手动确认")])),_:1,__:[29]})]),_:1},8,["modelValue"])]),e("div",null,[r[31]||(r[31]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-2"},"产品选择",-1)),a(te,{modelValue:x.value[p.value].settings.productSelection,"onUpdate:modelValue":r[5]||(r[5]=h=>x.value[p.value].settings.productSelection=h),placeholder:"选择产品来源",size:"small",class:"w-full"},{default:i(()=>[a(X,{label:"使用上一步结果",value:"previous"}),a(X,{label:"手动选择",value:"manual"}),a(X,{label:"全部产品",value:"all"})]),_:1},8,["modelValue"])]),e("div",null,[r[32]||(r[32]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"超时时间（分钟）",-1)),a(be,{modelValue:x.value[p.value].settings.timeout,"onUpdate:modelValue":r[6]||(r[6]=h=>x.value[p.value].settings.timeout=h),min:1,max:60,size:"small",class:"w-full"},null,8,["modelValue"])]),e("div",null,[r[33]||(r[33]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"失败处理",-1)),a(te,{modelValue:x.value[p.value].settings.onError,"onUpdate:modelValue":r[7]||(r[7]=h=>x.value[p.value].settings.onError=h),size:"small",class:"w-full"},{default:i(()=>[a(X,{label:"停止工作流",value:"stop"}),a(X,{label:"跳过继续",value:"skip"}),a(X,{label:"重试",value:"retry"})]),_:1},8,["modelValue"])])])])])):(o(),d("div",pr,[a(z(Te),{class:"w-12 h-12 mx-auto mb-2 opacity-50"}),r[34]||(r[34]=e("p",{class:"text-sm"},"选择一个应用节点进行设置",-1))]))])]),a(gt,{modelValue:f.value,"onUpdate:modelValue":r[8]||(r[8]=h=>f.value=h),onSelect:n,onCreateBlank:r[9]||(r[9]=h=>f.value=!1)},null,8,["modelValue"])]),_:1},8,["modelValue"])}}}),mr=ne(xr,[["__scopeId","data-v-51f4898f"]]),kr={key:0,class:"space-y-6"},br={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},vr={class:"grid grid-cols-2 gap-4"},fr={class:"text-sm text-gray-900 dark:text-dark-text"},hr={class:"text-sm text-gray-900 dark:text-dark-text"},yr={class:"text-sm text-gray-900 dark:text-dark-text"},wr={class:"text-sm text-gray-900 dark:text-dark-text"},_r={class:"text-sm text-gray-900 dark:text-dark-text"},$r={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},Cr={class:"p-4"},Vr={class:"space-y-4"},jr={class:"flex flex-col items-center"},Mr={class:"flex-1 min-w-0"},Tr={class:"flex items-center justify-between mb-2"},Sr={class:"flex items-center space-x-2"},Dr={class:"text-base font-medium text-gray-900 dark:text-dark-text"},zr={class:"grid grid-cols-2 gap-4 text-sm"},Er={key:0},Ir={class:"text-gray-900 dark:text-dark-text"},Br={key:1},Nr={class:"text-gray-900 dark:text-dark-text"},Lr={key:2},Wr={class:"text-gray-900 dark:text-dark-text"},Rr={key:3},Ar={class:"text-gray-900 dark:text-dark-text"},Ur={key:0,class:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 rounded border border-red-200 dark:border-red-800"},Pr={class:"text-sm text-red-700 dark:text-red-300"},Hr={class:"flex justify-end"},Fr=le({__name:"ExecutionDetailsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(B,{emit:j}){const N=B,D=j,E=W({get:()=>N.modelValue,set:m=>D("update:modelValue",m)}),M=m=>({"product-collection":ce,"smart-crop":ge,"one-click-cutout":xe,"super-split":U,"title-generator":pe,"batch-listing":ue,"pod-compose":U})[m]||U,V=m=>({商品采集:"product-collection",智能裁图:"smart-crop",一键抠图:"one-click-cutout",超级裂变:"super-split",标题生成:"title-generator",批量刊登:"batch-listing",POD合成:"pod-compose"})[m]||"unknown",p=m=>{switch(m){case"completed":return"success";case"failed":return"danger";case"running":return"warning";case"pending":return"info";default:return"info"}},f=m=>{switch(m){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},I=m=>p(m),R=m=>f(m),x=m=>{switch(m){case"completed":return"bg-green-500 text-white";case"failed":return"bg-red-500 text-white";case"running":return"bg-blue-500 text-white";case"pending":return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300";default:return"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300"}},L=m=>{switch(m){case"completed":return"bg-green-300 dark:bg-green-600";case"failed":return"bg-red-300 dark:bg-red-600";case"running":return"bg-blue-300 dark:bg-blue-600";default:return"bg-gray-300 dark:bg-gray-600"}},y=()=>{E.value=!1};return(m,c)=>{const v=_("el-tag"),n=_("el-button"),b=_("el-dialog");return o(),H(b,{modelValue:E.value,"onUpdate:modelValue":c[0]||(c[0]=g=>E.value=g),title:"执行详情",width:"800px","close-on-click-modal":!1,class:"execution-dialog"},{footer:i(()=>[e("div",Hr,[a(n,{onClick:y,size:"large"},{default:i(()=>c[14]||(c[14]=[w("关闭")])),_:1,__:[14]})])]),default:i(()=>[m.execution?(o(),d("div",kr,[e("div",br,[c[7]||(c[7]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"基本信息",-1)),e("div",vr,[e("div",null,[c[1]||(c[1]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行ID",-1)),e("p",fr,s(m.execution.id),1)]),e("div",null,[c[2]||(c[2]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"工作流名称",-1)),e("p",hr,s(m.execution.workflowName),1)]),e("div",null,[c[3]||(c[3]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行状态",-1)),a(v,{type:p(m.execution.status),size:"small"},{default:i(()=>[w(s(f(m.execution.status)),1)]),_:1},8,["type"])]),e("div",null,[c[4]||(c[4]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行人",-1)),e("p",yr,s(m.execution.executor),1)]),e("div",null,[c[5]||(c[5]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"开始时间",-1)),e("p",wr,s(m.execution.startTime),1)]),e("div",null,[c[6]||(c[6]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text mb-1"},"执行时长",-1)),e("p",_r,s(m.execution.duration||"进行中"),1)])])]),e("div",$r,[c[13]||(c[13]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text p-4 border-b border-gray-200 dark:border-dark-border"}," 执行步骤 ",-1)),e("div",Cr,[e("div",Vr,[(o(!0),d(P,null,Z(m.execution.stepResults,(g,F)=>(o(),d("div",{key:g.appId,class:"flex items-start space-x-4"},[e("div",jr,[e("div",{class:q(["w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",x(g.status)])},s(F+1),3),F<m.execution.stepResults.length-1?(o(),d("div",{key:0,class:q(["w-px h-12 mt-2",L(g.status)])},null,2)):A("",!0)]),e("div",Mr,[e("div",Tr,[e("div",Sr,[(o(),H(Q(M(V(g.appName))),{class:"w-5 h-5 text-blue-600 dark:text-blue-400"})),e("h4",Dr,s(g.appName),1),a(v,{type:I(g.status),size:"small"},{default:i(()=>[w(s(R(g.status)),1)]),_:2},1032,["type"])])]),e("div",zr,[g.startTime?(o(),d("div",Er,[c[8]||(c[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"开始时间：",-1)),e("span",Ir,s(g.startTime),1)])):A("",!0),g.duration?(o(),d("div",Br,[c[9]||(c[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"执行时长：",-1)),e("span",Nr,s(g.duration),1)])):A("",!0),g.inputCount!==void 0?(o(),d("div",Lr,[c[10]||(c[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输入数量：",-1)),e("span",Wr,s(g.inputCount),1)])):A("",!0),g.outputCount!==void 0?(o(),d("div",Rr,[c[11]||(c[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"输出数量：",-1)),e("span",Ar,s(g.outputCount),1)])):A("",!0)]),g.errorMessage?(o(),d("div",Ur,[e("p",Pr,[c[12]||(c[12]=e("strong",null,"错误信息：",-1)),w(s(g.errorMessage),1)])])):A("",!0)])]))),128))])])])])):A("",!0)]),_:1},8,["modelValue"])}}}),Or=ne(Fr,[["__scopeId","data-v-5b52e5e1"]]),Zr={key:0,class:"space-y-6"},Xr={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},qr={class:"grid grid-cols-4 gap-4"},Kr={class:"text-center"},Jr={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Qr={class:"text-center"},Gr={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Yr={class:"text-center"},ea={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ta={class:"text-center"},ra={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},aa={class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border"},oa={class:"p-4"},sa={class:"space-y-4"},la={class:"grid grid-cols-3 gap-4"},na={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3 text-center"},da={class:"text-lg font-semibold text-blue-600 dark:text-blue-400"},ia={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-3 text-center"},ua={class:"text-lg font-semibold text-green-600 dark:text-green-400"},ca={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-3 text-center"},pa={class:"text-lg font-semibold text-orange-600 dark:text-orange-400"},ga={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},xa={key:0},ma={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"},ka=["src","alt"],ba={class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate"},va={class:"text-sm text-green-600 dark:text-green-400 font-semibold"},fa={key:1},ha={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},ya=["src","alt"],wa={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center"},_a={key:2},$a={class:"space-y-2"},Ca={class:"text-sm text-gray-900 dark:text-dark-text"},Va={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1"},ja={key:3},Ma={class:"space-y-2"},Ta={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Sa={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Da={key:4},za={class:"flex justify-between"},Ea=le({__name:"ResultsDialog",props:{modelValue:{type:Boolean},execution:{}},emits:["update:modelValue"],setup(B,{emit:j}){const N=B,D=j,E=W({get:()=>N.modelValue,set:v=>D("update:modelValue",v)}),M=S(""),V=W(()=>(N.execution&&N.execution.stepResults.length>0&&(M.value=N.execution.stepResults[0].appId),N.execution)),p=()=>{if(!V.value)return 0;const v=V.value.stepResults[0];return(v==null?void 0:v.inputCount)||0},f=()=>{if(!V.value)return 0;const v=V.value.stepResults[V.value.stepResults.length-1];return(v==null?void 0:v.outputCount)||0},I=()=>{const v=p(),n=f();return v===0?0:Math.round(n/v*100)},R=v=>Array.from({length:Math.min(v,6)},(n,b)=>({id:`product_${b+1}`,title:`商品标题 ${b+1} - 高质量产品描述`,price:(Math.random()*100+10).toFixed(2),image:`https://picsum.photos/200/200?random=${b+1}`})),x=v=>Array.from({length:Math.min(v,8)},(n,b)=>({id:`image_${b+1}`,name:`处理后图片_${b+1}.jpg`,image:`https://picsum.photos/150/150?random=${b+10}`})),L=v=>{const n=["高品质时尚T恤 - 舒适透气 多色可选 男女通用款式","精美陶瓷马克杯 - 创意设计 办公室必备 礼品首选","多功能手机壳 - 防摔保护 时尚外观 适配多机型","舒适运动鞋 - 轻便透气 专业运动 日常休闲两用","优质帆布包 - 大容量设计 环保材质 时尚百搭"];return Array.from({length:Math.min(v,5)},(b,g)=>({id:`title_${g+1}`,title:n[g%n.length],length:n[g%n.length].length}))},y=v=>{const n=["Amazon","eBay","Shopify","Etsy"];return Array.from({length:Math.min(v,8)},(b,g)=>({id:`listing_${g+1}`,platform:n[g%n.length],productId:`PRD${String(g+1).padStart(6,"0")}`,status:Math.random()>.2?"success":"failed"}))},m=()=>{T.success("结果下载功能开发中...")},c=()=>{E.value=!1};return(v,n)=>{const b=_("el-tag"),g=_("el-tab-pane"),F=_("el-tabs"),K=_("el-button"),ee=_("el-dialog");return o(),H(ee,{modelValue:E.value,"onUpdate:modelValue":n[1]||(n[1]=$=>E.value=$),title:"处理结果",width:"1000px","close-on-click-modal":!1,class:"results-dialog"},{footer:i(()=>[e("div",za,[a(K,{onClick:m,type:"primary",plain:""},{default:i(()=>n[13]||(n[13]=[w(" 下载结果 ")])),_:1,__:[13]}),a(K,{onClick:c,size:"large"},{default:i(()=>n[14]||(n[14]=[w("关闭")])),_:1,__:[14]})])]),default:i(()=>[V.value?(o(),d("div",Zr,[e("div",Xr,[n[6]||(n[6]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-3"},"处理概览",-1)),e("div",qr,[e("div",Kr,[e("div",Jr,s(p()),1),n[2]||(n[2]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输入",-1))]),e("div",Qr,[e("div",Gr,s(f()),1),n[3]||(n[3]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总输出",-1))]),e("div",Yr,[e("div",ea,s(I())+"%",1),n[4]||(n[4]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"成功率",-1))]),e("div",ta,[e("div",ra,s(V.value.duration||"0分钟"),1),n[5]||(n[5]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总耗时",-1))])])]),e("div",aa,[n[12]||(n[12]=e("div",{class:"p-4 border-b border-gray-200 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"各步骤处理结果")],-1)),e("div",oa,[a(F,{modelValue:M.value,"onUpdate:modelValue":n[0]||(n[0]=$=>M.value=$),type:"border-card"},{default:i(()=>[(o(!0),d(P,null,Z(V.value.stepResults,$=>(o(),H(g,{key:$.appId,label:$.appName,name:$.appId},{default:i(()=>[e("div",sa,[e("div",la,[e("div",na,[e("div",da,s($.inputCount||0),1),n[7]||(n[7]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输入数量",-1))]),e("div",ia,[e("div",ua,s($.outputCount||0),1),n[8]||(n[8]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"输出数量",-1))]),e("div",ca,[e("div",pa,s($.duration||"0分钟"),1),n[9]||(n[9]=e("div",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"处理时长",-1))])]),e("div",ga,[n[11]||(n[11]=e("h4",{class:"text-base font-medium text-gray-900 dark:text-dark-text mb-3"},"处理结果数据",-1)),$.appName==="商品采集"?(o(),d("div",xa,[e("div",ma,[(o(!0),d(P,null,Z(R($.outputCount||0),C=>(o(),d("div",{key:C.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("img",{src:C.image,alt:C.title,class:"w-full h-32 object-cover rounded mb-2"},null,8,ka),e("h5",ba,s(C.title),1),e("p",va,"$"+s(C.price),1)]))),128))])])):$.appName==="智能裁图"||$.appName==="一键抠图"?(o(),d("div",fa,[e("div",ha,[(o(!0),d(P,null,Z(x($.outputCount||0),C=>(o(),d("div",{key:C.id,class:"bg-white dark:bg-dark-surface rounded-lg p-2 border border-gray-200 dark:border-dark-border"},[e("img",{src:C.image,alt:C.name,class:"w-full h-24 object-cover rounded mb-1"},null,8,ya),e("p",wa,s(C.name),1)]))),128))])])):$.appName==="标题生成"?(o(),d("div",_a,[e("div",$a,[(o(!0),d(P,null,Z(L($.outputCount||0),C=>(o(),d("div",{key:C.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border"},[e("p",Ca,s(C.title),1),e("p",Va,"长度: "+s(C.length)+" 字符",1)]))),128))])])):$.appName==="批量刊登"?(o(),d("div",ja,[e("div",Ma,[(o(!0),d(P,null,Z(y($.outputCount||0),C=>(o(),d("div",{key:C.id,class:"bg-white dark:bg-dark-surface rounded-lg p-3 border border-gray-200 dark:border-dark-border flex justify-between items-center"},[e("div",null,[e("p",Ta,s(C.platform),1),e("p",Sa,s(C.productId),1)]),a(b,{type:C.status==="success"?"success":"danger",size:"small"},{default:i(()=>[w(s(C.status==="success"?"成功":"失败"),1)]),_:2},1032,["type"])]))),128))])])):(o(),d("div",Da,n[10]||(n[10]=[e("p",{class:"text-gray-500 dark:text-dark-text-secondary text-center py-4"}," 暂无结果数据展示 ",-1)])))])])]),_:2},1032,["label","name"]))),128))]),_:1},8,["modelValue"])])])])):A("",!0)]),_:1},8,["modelValue"])}}}),Ia=ne(Ea,[["__scopeId","data-v-392a9730"]]),Ba={class:"space-y-6"},Na={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},La={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Wa={class:"flex items-center justify-between"},Ra={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Aa={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ua={class:"flex items-center justify-between"},Pa={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Ha={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Fa={class:"flex items-center justify-between"},Oa={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},Za={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Xa={class:"flex items-center justify-between"},qa={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Ka={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Ja={class:"flex justify-between items-center"},Qa={class:"flex items-center space-x-3"},Ga={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},Ya={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},eo={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6"},to={class:"flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6"},ro={class:"flex flex-col sm:flex-row gap-4 flex-1"},ao={class:"relative flex-1 max-w-md"},oo={class:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none"},so={class:"relative"},lo={class:"flex items-center space-x-2"},no={class:"flex items-center justify-between lg:justify-end gap-4"},io={class:"flex items-center space-x-4"},uo={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},co={class:"font-semibold text-gray-900 dark:text-dark-text"},po={key:0,class:"flex items-center gap-3"},go={class:"text-sm font-medium text-blue-600 dark:text-blue-400"},xo={class:"bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},mo={class:"flex items-center space-x-3"},ko={class:"font-medium text-gray-900 dark:text-dark-text"},bo={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},vo={class:"py-2"},fo={class:"flex items-center space-x-2 max-w-full"},ho={class:"flex flex-col items-center space-y-1 flex-shrink-0"},yo={class:"w-6 h-6 bg-green-500 rounded-full flex items-center justify-center"},wo={class:"flex items-center space-x-1 min-w-0 flex-1"},_o={class:"flex flex-col items-center space-y-1 flex-shrink-0"},$o={class:"text-xs text-gray-600 dark:text-dark-text-secondary text-center max-w-12 truncate"},Co={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Vo={class:"text-xs text-gray-600 dark:text-dark-text-secondary"},jo={class:"flex flex-col items-center space-y-1 flex-shrink-0"},Mo={class:"w-6 h-6 bg-red-500 rounded-full flex items-center justify-center"},To={class:"space-y-2"},So={key:0,class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Do={class:"space-y-1"},zo={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Eo={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Io={class:"flex items-center space-x-2"},Bo=["onClick"],No={class:"px-6 py-4 border-t border-gray-100 dark:border-dark-border"},Lo=le({__name:"index",setup(B){const j=S(""),N=S(""),D=S([]),E=S(!1),M=S(!1),V=S(!1),p=S(null),f=S({currentPage:1,pageSize:20,total:0}),I=S([]),R=S(!1),x=W(()=>{let l=I.value;return j.value&&(l=l.filter(t=>t.workflowName.toLowerCase().includes(j.value.toLowerCase())||t.id.toLowerCase().includes(j.value.toLowerCase()))),N.value&&(l=l.filter(t=>t.status===N.value)),l}),L=W(()=>{const l=(f.value.currentPage-1)*f.value.pageSize,t=l+f.value.pageSize;return x.value.slice(l,t)}),y=W(()=>I.value.length),m=W(()=>{if(I.value.length===0)return 0;const l=I.value.filter(t=>t.status==="completed").length;return Math.round(l/I.value.length*100)}),c=W(()=>new Set(I.value.map(t=>t.workflowId)).size),v=W(()=>I.value.filter(t=>t.status==="completed"&&t.duration).length===0?"0分钟":"3.5分钟"),n=l=>({"product-collection":ce,"smart-crop":ge,"one-click-cutout":xe,"super-split":U,"title-generator":pe,"batch-listing":ue,"pod-compose":U})[l]||U,b=l=>{switch(l){case"completed":return"已完成";case"failed":return"失败";case"running":return"执行中";case"pending":return"等待中";default:return"未知"}},g=async()=>{R.value=!0;try{const l=await qe();I.value=l,f.value.total=x.value.length}catch{T.error("加载执行历史失败")}finally{R.value=!1}},F=l=>{D.value=l},K=()=>{f.value.currentPage=1,f.value.total=x.value.length},ee=l=>{p.value=l,M.value=!0},$=l=>{p.value=l,V.value=!0},C=l=>{switch(l){case"completed":return"bg-green-100 dark:bg-green-900/30";case"failed":return"bg-red-100 dark:bg-red-900/30";case"running":return"bg-blue-100 dark:bg-blue-900/30";case"pending":return"bg-gray-100 dark:bg-gray-900/30";default:return"bg-gray-100 dark:bg-gray-900/30"}},me=l=>{switch(l){case"completed":return"svg";case"failed":return"svg";case"running":return"svg";default:return"svg"}},ke=l=>{switch(l){case"completed":return"text-green-600 dark:text-green-400";case"failed":return"text-red-600 dark:text-red-400";case"running":return"text-blue-600 dark:text-blue-400";case"pending":return"text-gray-600 dark:text-gray-400";default:return"text-gray-600 dark:text-gray-400"}},de=l=>{switch(l){case"completed":return"bg-green-500";case"failed":return"bg-red-500";case"running":return"bg-blue-500";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},k=l=>{switch(l){case"completed":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";case"failed":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"running":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"pending":return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},r=l=>{switch(l){case"completed":return"bg-green-400";case"failed":return"bg-red-400";case"running":return"bg-blue-400";case"pending":return"bg-gray-400";default:return"bg-gray-400"}},O=l=>{const{action:t,row:J}=l;switch(t){case"viewResults":$(J);break;case"rerun":T.info("重新执行功能开发中...");break;case"duplicate":T.info("复制工作流功能开发中...");break;case"export":T.info("导出结果功能开发中...");break;case"delete":T.info("删除记录功能开发中...");break}},G=()=>{T.success("导出执行历史功能开发中...")},ie=()=>{T.success("数据已刷新！"),g()},X=l=>{switch(l){case"today":T.info("显示今日执行记录");break;case"week":T.info("显示本周执行记录");break;default:return}K()},te=()=>{if(D.value.length===0){T.warning("请先选择要导出的记录");return}T.success(`正在导出 ${D.value.length} 条执行记录...`)},be=()=>{if(D.value.length===0){T.warning("请先选择要删除的记录");return}T.warning(`批量删除功能开发中... (已选择 ${D.value.length} 项)`)},ve=l=>{f.value.pageSize=l,f.value.currentPage=1,g()},h=l=>{f.value.currentPage=l,g()};return Ae(()=>{Fe(),g()}),(l,t)=>{const J=_("el-table-column"),re=_("el-dropdown-item"),ze=_("el-dropdown-menu"),Ee=_("el-dropdown"),Ie=_("el-table"),Be=_("el-pagination"),Ne=He("loading");return o(),d("div",Ba,[t[39]||(t[39]=he('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-bc331a57><div class="flex items-center space-x-3" data-v-bc331a57><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-bc331a57><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-bc331a57><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" data-v-bc331a57></path></svg></div><div data-v-bc331a57><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-bc331a57>工作流管理</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-bc331a57>创建、管理和监控您的自动化工作流程</p></div></div></div>',1)),e("div",Na,[e("div",La,[e("div",Wa,[e("div",null,[t[10]||(t[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总执行次数",-1)),e("p",Ra,s(y.value),1)]),t[11]||(t[11]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"})])],-1))])]),e("div",Aa,[e("div",Ua,[e("div",null,[t[12]||(t[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Pa,s(m.value)+"%",1)]),t[13]||(t[13]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Ha,[e("div",Fa,[e("div",null,[t[14]||(t[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"活跃工作流",-1)),e("p",Oa,s(c.value),1)]),t[15]||(t[15]=e("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})])],-1))])]),e("div",Za,[e("div",Xa,[e("div",null,[t[16]||(t[16]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"平均耗时",-1)),e("p",qa,s(v.value),1)]),t[17]||(t[17]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Ka,[e("div",Ja,[e("div",Qa,[e("button",{onClick:t[0]||(t[0]=u=>E.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[a(z(Ue),{class:"w-5 h-5 mr-2"}),t[18]||(t[18]=w(" 新建工作流 "))]),e("button",{onClick:G,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[a(z(Se),{class:"w-5 h-5 mr-2"}),t[19]||(t[19]=w(" 导出数据 "))]),D.value.length>0?(o(),d("div",Ga,[e("span",Ya," 已选择 "+s(D.value.length)+" 项 ",1),e("button",{onClick:te,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[a(z(Se),{class:"w-4 h-4 mr-1"}),t[20]||(t[20]=w(" 批量导出 "))])])):A("",!0)])])]),e("div",eo,[e("div",to,[e("div",ro,[e("div",ao,[e("div",oo,[a(z(_e),{class:"w-5 h-5 text-gray-400"})]),ae(e("input",{"onUpdate:modelValue":t[1]||(t[1]=u=>j.value=u),onInput:K,type:"text",placeholder:"搜索工作流名称或ID...",class:"w-full pl-12 pr-4 py-3 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 text-gray-900 dark:text-dark-text placeholder-gray-500 hover:border-gray-300 dark:hover:border-dark-text-secondary"},null,544),[[ye,j.value]])]),e("div",so,[ae(e("select",{"onUpdate:modelValue":t[2]||(t[2]=u=>N.value=u),onChange:K,class:"appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"},t[21]||(t[21]=[he('<option value="" data-v-bc331a57>全部状态</option><option value="completed" data-v-bc331a57>已完成</option><option value="running" data-v-bc331a57>执行中</option><option value="failed" data-v-bc331a57>失败</option><option value="pending" data-v-bc331a57>等待中</option>',5)]),544),[[Pe,N.value]]),t[22]||(t[22]=e("div",{class:"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none"},[e("svg",{class:"w-5 h-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),e("div",lo,[e("button",{onClick:t[3]||(t[3]=u=>X("today")),class:"px-3 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-blue-600 dark:hover:text-blue-400 bg-gray-100 dark:bg-dark-surface hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"}," 今日 "),e("button",{onClick:t[4]||(t[4]=u=>X("week")),class:"px-3 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-blue-600 dark:hover:text-blue-400 bg-gray-100 dark:bg-dark-surface hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200"}," 本周 ")])]),e("div",no,[e("div",io,[e("div",uo,[t[23]||(t[23]=w(" 共 ")),e("span",co,s(f.value.total),1),t[24]||(t[24]=w(" 条记录 "))]),D.value.length>0?(o(),d("div",po,[e("span",go," 已选择 "+s(D.value.length)+" 项 ",1),e("div",{class:"flex space-x-2"},[e("button",{onClick:te,class:"px-3 py-2 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200 transform hover:scale-105"}," 批量导出 "),e("button",{onClick:be,class:"px-3 py-2 text-sm font-medium text-red-600 dark:text-red-400 bg-red-50 dark:bg-red-900/20 hover:bg-red-100 dark:hover:bg-red-900/30 rounded-lg transition-all duration-200 transform hover:scale-105"}," 批量删除 ")])])):A("",!0)])])])]),e("div",xo,[e("div",{class:"px-6 py-5 border-b border-gray-100 dark:border-dark-border bg-gradient-to-r from-gray-50 to-gray-100 dark:from-dark-surface dark:to-dark-border"},[e("div",{class:"flex items-center justify-between"},[t[26]||(t[26]=he('<div class="flex items-center space-x-3" data-v-bc331a57><div class="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center" data-v-bc331a57><svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-bc331a57><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" data-v-bc331a57></path></svg></div><h3 class="text-xl font-bold text-gray-900 dark:text-dark-text" data-v-bc331a57>执行历史</h3></div>',1)),e("div",{class:"flex items-center space-x-2"},[e("button",{onClick:ie,class:"p-2 text-gray-500 dark:text-dark-text-secondary hover:text-blue-600 dark:hover:text-blue-400 hover:bg-blue-50 dark:hover:bg-blue-900/20 rounded-lg transition-all duration-200",title:"刷新数据"},t[25]||(t[25]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})],-1)]))])])]),ae((o(),H(Ie,{data:L.value,onSelectionChange:F,class:"w-full workflow-table","header-cell-style":{backgroundColor:"transparent",color:"#6b7280",fontWeight:"500",borderBottom:"1px solid #f3f4f6"},"row-style":{height:"72px"}},{default:i(()=>[a(J,{type:"selection",width:"55"}),a(J,{label:"执行信息","min-width":"250"},{default:i(u=>[e("div",mo,[e("div",{class:q(["w-10 h-10 rounded-lg flex items-center justify-center",C(u.row.status)])},[(o(),H(Q(me(u.row.status)),{class:q(["w-5 h-5",ke(u.row.status)])},null,8,["class"]))],2),e("div",null,[e("div",ko,s(u.row.workflowName),1),e("div",bo,"ID: "+s(u.row.id),1)])])]),_:1}),a(J,{label:"工作流程",width:"280"},{default:i(({row:u})=>[e("div",vo,[e("div",fo,[e("div",ho,[e("div",yo,[a(z($e),{class:"w-3 h-3 text-white"})]),t[27]||(t[27]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"开始",-1))]),e("div",wo,[(o(!0),d(P,null,Z(u.workflow.apps.slice(0,3),(fe,Ve)=>{var je;return o(),d(P,{key:Ve},[t[28]||(t[28]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",_o,[e("div",{class:q(["w-6 h-6 rounded-full flex items-center justify-center",de((je=u.stepResults[Ve])==null?void 0:je.status)])},[(o(),H(Q(n(fe.type)),{class:"w-3 h-3 text-white"}))],2),e("span",$o,s(fe.name),1)])],64)}),128)),u.workflow.apps.length>3?(o(),d(P,{key:0},[t[30]||(t[30]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",Co,[t[29]||(t[29]=e("div",{class:"w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center"},[e("span",{class:"text-xs text-white font-bold"},"...")],-1)),e("span",Vo," +"+s(u.workflow.apps.length-3),1)])],64)):A("",!0)]),t[32]||(t[32]=e("div",{class:"w-4 h-px bg-gray-300 dark:bg-gray-600 flex-shrink-0"},null,-1)),e("div",jo,[e("div",Mo,[a(z(Ce),{class:"w-3 h-3 text-white"})]),t[31]||(t[31]=e("span",{class:"text-xs text-gray-600 dark:text-dark-text-secondary"},"结束",-1))])])])]),_:1}),a(J,{label:"执行状态",width:"150"},{default:i(u=>[e("div",To,[e("span",{class:q(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",k(u.row.status)])},[e("span",{class:q(["w-1.5 h-1.5 rounded-full mr-1.5",r(u.row.status)])},null,2),w(" "+s(b(u.row.status)),1)],2),u.row.duration?(o(),d("div",So," 耗时: "+s(u.row.duration),1)):A("",!0)])]),_:1}),a(J,{label:"执行信息",width:"180"},{default:i(u=>[e("div",Do,[e("div",zo,s(u.row.executor),1),e("div",Eo,s(u.row.startTime),1)])]),_:1}),a(J,{label:"操作",width:"180"},{default:i(u=>[e("div",Io,[e("button",{onClick:fe=>ee(u.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Bo),a(Ee,{onCommand:O,trigger:"click"},{dropdown:i(()=>[a(ze,null,{default:i(()=>[a(re,{command:{action:"viewResults",row:u.row},disabled:u.row.status!=="completed"},{default:i(()=>t[33]||(t[33]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})]),e("span",null,"处理结果")],-1)])),_:2,__:[33]},1032,["command","disabled"]),a(re,{command:{action:"rerun",row:u.row}},{default:i(()=>t[34]||(t[34]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),e("span",null,"重新执行")],-1)])),_:2,__:[34]},1032,["command"]),a(re,{command:{action:"duplicate",row:u.row}},{default:i(()=>t[35]||(t[35]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})]),e("span",null,"复制工作流")],-1)])),_:2,__:[35]},1032,["command"]),a(re,{command:{action:"export",row:u.row}},{default:i(()=>t[36]||(t[36]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),e("span",null,"导出结果")],-1)])),_:2,__:[36]},1032,["command"]),a(re,{command:{action:"delete",row:u.row},divided:""},{default:i(()=>t[37]||(t[37]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})]),e("span",null,"删除记录")],-1)])),_:2,__:[37]},1032,["command"])]),_:2},1024)]),default:i(()=>[t[38]||(t[38]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[w(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[38]},1024)])]),_:1})]),_:1},8,["data"])),[[Ne,R.value]]),e("div",No,[a(Be,{"current-page":f.value.currentPage,"onUpdate:currentPage":t[5]||(t[5]=u=>f.value.currentPage=u),"page-size":f.value.pageSize,"onUpdate:pageSize":t[6]||(t[6]=u=>f.value.pageSize=u),total:f.value.total,"page-sizes":[10,20,50,100],layout:"total, sizes, prev, pager, next, jumper",onSizeChange:ve,onCurrentChange:h},null,8,["current-page","page-size","total"])])]),a(mr,{modelValue:E.value,"onUpdate:modelValue":t[7]||(t[7]=u=>E.value=u),onSuccess:ie},null,8,["modelValue"]),a(Or,{modelValue:M.value,"onUpdate:modelValue":t[8]||(t[8]=u=>M.value=u),execution:p.value,onViewResults:$},null,8,["modelValue","execution"]),a(Ia,{modelValue:V.value,"onUpdate:modelValue":t[9]||(t[9]=u=>V.value=u),execution:p.value},null,8,["modelValue","execution"])])}}}),Uo=ne(Lo,[["__scopeId","data-v-bc331a57"]]);export{Uo as default};
