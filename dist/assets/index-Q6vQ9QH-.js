import{c as l,a as e,o as d,d as R,r as o,e as A,f as W,m as q,g as c,t as r,b as I,N as T,F as K,k as Y,A as j,z as D,j as v,h as w,E as x,Y as M,i as B,u as Z,M as $,n as S,q as G,s as J,C as P}from"./index-gGKuRSZs.js";function U(k,m){return d(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M9 17.25v1.007a3 3 0 0 1-.879 2.122L7.5 21h9l-.621-.621A3 3 0 0 1 15 18.257V17.25m6-12V15a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 15V5.25m18 0A2.25 2.25 0 0 0 18.75 3H5.25A2.25 2.25 0 0 0 3 5.25m18 0V12a2.25 2.25 0 0 1-2.25 2.25H5.25A2.25 2.25 0 0 1 3 12V5.25"})])}function Q(k,m){return d(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 1.5H8.25A2.25 2.25 0 0 0 6 3.75v16.5a2.25 2.25 0 0 0 2.25 2.25h7.5A2.25 2.25 0 0 0 18 20.25V3.75a2.25 2.25 0 0 0-2.25-2.25H13.5m-3 0V3h3V1.5m-3 0h3m-3 18.75h3"})])}function X(k,m){return d(),l("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor","aria-hidden":"true","data-slot":"icon"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M10.5 19.5h3m-6.75 2.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-15a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 4.5v15a2.25 2.25 0 0 0 2.25 2.25Z"})])}const ee={class:"p-8"},te={class:"max-w-2xl"},ae={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm mb-6"},se={class:"flex items-center space-x-4 mb-6"},re=["src"],oe={class:"text-lg font-medium text-gray-900 dark:text-dark-text"},de={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ne={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},le={class:"grid grid-cols-1 md:grid-cols-2 gap-4 text-sm"},ie={class:"flex justify-between"},ce={class:"text-gray-900 dark:text-dark-text"},ue={class:"flex justify-between"},xe={class:"text-gray-900 dark:text-dark-text"},me={class:"flex justify-between"},ge={class:"text-gray-900 dark:text-dark-text"},ve={class:"flex justify-between"},ke={class:"text-gray-900 dark:text-dark-text"},pe={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm mb-6"},fe={class:"space-y-4"},ye={class:"flex items-start space-x-3 cursor-pointer"},be={class:"flex items-start space-x-3 cursor-pointer"},he={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm mb-6"},we={class:"space-y-4"},_e={class:"flex items-center space-x-4"},Ce={class:"flex items-center space-x-2"},Ve={class:"font-medium text-gray-900 dark:text-dark-text"},Ae={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400"},Ie={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Te={class:"text-xs text-gray-400"},je=["onClick"],De={class:"bg-red-50 dark:bg-red-900/10 border border-red-200 dark:border-red-800 rounded-lg p-6"},Me={class:"flex items-start space-x-3"},Be={class:"flex-1"},$e={class:"flex justify-end space-x-3 mt-8"},Se=["loading"],Pe={class:"space-y-4"},Ue={class:"flex items-start space-x-3"},He={class:"flex justify-end space-x-3"},ze=R({__name:"index",setup(k){const m=Z(),p=o(!1),f=o(!1),g=o(!1),u=o("current"),y=o(""),b=o(""),_=o(Date.now()),i=o({nickname:"张三",email:"<EMAIL>",accountId:"ACC20240001",avatar:"/default-avatar.png",loginTime:"2024-01-15 09:30:00",loginIp:"*************",deviceInfo:"Chrome 120.0 on Windows 10"}),h=o([{id:"session1",deviceType:"desktop",deviceInfo:"Chrome 120.0 on Windows 10",location:"北京市",ip:"*************",lastActive:"刚刚",isCurrent:!0},{id:"session2",deviceType:"mobile",deviceInfo:"Safari on iPhone 15",location:"上海市",ip:"*************",lastActive:"2小时前",isCurrent:!1},{id:"session3",deviceType:"tablet",deviceInfo:"Chrome on iPad",location:"广州市",ip:"*************",lastActive:"1天前",isCurrent:!1}]),H=A(()=>{const s=Date.now()-_.value,t=Math.floor(s/(1e3*60*60)),n=Math.floor(s%(1e3*60*60)/(1e3*60));return`${t}小时${n}分钟`}),C=A(()=>y.value&&b.value==="确认注销"),N=s=>({desktop:U,mobile:Q,tablet:X})[s]||U,E=()=>{x.success("会话信息已刷新")},z=async s=>{try{await M.confirm(`确定要终止 "${s.deviceInfo}" 的登录会话吗？`,"确认终止会话",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(n=>setTimeout(n,500));const t=h.value.findIndex(n=>n.id===s.id);t>-1&&h.value.splice(t,1),x.success("会话已终止")}catch{}},L=async()=>{try{await M.confirm(u.value==="all"?"确定要退出所有设备的登录吗？":"确定要退出当前登录吗？","确认退出",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),p.value=!0,await new Promise(s=>setTimeout(s,1e3)),localStorage.removeItem("user-token"),B.value=!1,x.success("退出登录成功"),m.push("/login")}catch{}finally{p.value=!1}},F=async()=>{if(!C.value){x.warning("请完成所有确认步骤");return}f.value=!0;try{await new Promise(s=>setTimeout(s,2e3)),localStorage.removeItem("user-token"),B.value=!1,x.success("账号注销成功"),m.push("/login")}catch{x.error("注销失败，请重试")}finally{f.value=!1}};return W(()=>{_.value=Date.now()-Math.random()*36e5}),q(()=>{}),(s,t)=>{const n=w("el-input"),V=w("el-button"),O=w("el-dialog");return d(),l("div",ee,[t[24]||(t[24]=e("div",{class:"mb-8"},[e("h2",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},"退出登录"),e("p",{class:"mt-2 text-sm text-gray-600 dark:text-dark-text-secondary"},"安全退出您的账号")],-1)),e("div",te,[e("div",ae,[t[12]||(t[12]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"当前登录信息",-1)),e("div",se,[e("img",{src:i.value.avatar||"/default-avatar.png",alt:"头像",class:"w-16 h-16 rounded-full object-cover border-4 border-white dark:border-dark-border shadow-lg"},null,8,re),e("div",null,[e("h4",oe,r(i.value.nickname),1),e("p",de,r(i.value.email),1),e("p",ne,"账号ID: "+r(i.value.accountId),1)])]),e("div",le,[e("div",ie,[t[8]||(t[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"登录时间:",-1)),e("span",ce,r(i.value.loginTime),1)]),e("div",ue,[t[9]||(t[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"登录IP:",-1)),e("span",xe,r(i.value.loginIp),1)]),e("div",me,[t[10]||(t[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"设备信息:",-1)),e("span",ge,r(i.value.deviceInfo),1)]),e("div",ve,[t[11]||(t[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"在线时长:",-1)),e("span",ke,r(H.value),1)])])]),e("div",pe,[t[15]||(t[15]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text mb-4"},"退出选项",-1)),e("div",fe,[e("label",ye,[I(e("input",{type:"radio","onUpdate:modelValue":t[0]||(t[0]=a=>u.value=a),value:"current",class:"mt-1 text-amber-600 focus:ring-amber-500"},null,512),[[T,u.value]]),t[13]||(t[13]=e("div",null,[e("div",{class:"font-medium text-gray-900 dark:text-dark-text"},"仅退出当前设备"),e("div",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"}," 只退出当前浏览器，其他设备上的登录状态保持不变 ")],-1))]),e("label",be,[I(e("input",{type:"radio","onUpdate:modelValue":t[1]||(t[1]=a=>u.value=a),value:"all",class:"mt-1 text-amber-600 focus:ring-amber-500"},null,512),[[T,u.value]]),t[14]||(t[14]=e("div",null,[e("div",{class:"font-medium text-gray-900 dark:text-dark-text"},"退出所有设备"),e("div",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"}," 退出所有已登录的设备，需要重新登录才能访问 ")],-1))])])]),e("div",he,[e("div",{class:"flex items-center justify-between mb-4"},[t[16]||(t[16]=e("h3",{class:"text-lg font-medium text-gray-900 dark:text-dark-text"},"活跃会话",-1)),e("button",{onClick:E,class:"text-sm text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"}," 刷新 ")]),e("div",we,[(d(!0),l(K,null,Y(h.value,a=>(d(),l("div",{key:a.id,class:"flex items-center justify-between p-4 bg-gray-50 dark:bg-dark-border rounded-lg"},[e("div",_e,[e("div",{class:S(["w-10 h-10 rounded-full flex items-center justify-center",[a.isCurrent?"bg-green-100 dark:bg-green-900/20":"bg-gray-100 dark:bg-gray-800"]])},[(d(),G(J(N(a.deviceType)),{class:S(["w-5 h-5",[a.isCurrent?"text-green-600 dark:text-green-400":"text-gray-600 dark:text-gray-400"]])},null,8,["class"]))],2),e("div",null,[e("div",Ce,[e("span",Ve,r(a.deviceInfo),1),a.isCurrent?(d(),l("span",Ae," 当前设备 ")):$("",!0)]),e("div",Ie,r(a.location)+" · "+r(a.lastActive),1),e("div",Te,"IP: "+r(a.ip),1)])]),a.isCurrent?$("",!0):(d(),l("button",{key:0,onClick:Ne=>z(a),class:"text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"}," 终止 ",8,je))]))),128))])]),e("div",De,[e("div",Me,[c(j(D),{class:"w-6 h-6 text-red-600 dark:text-red-400 mt-0.5"}),e("div",Be,[t[17]||(t[17]=e("h3",{class:"text-lg font-medium text-red-900 dark:text-red-400 mb-2"},"注销账号",-1)),t[18]||(t[18]=e("p",{class:"text-sm text-red-700 dark:text-red-300 mb-4"}," 注销账号将永久删除您的所有数据，包括图库、商品、交易记录等，此操作不可恢复。 ",-1)),e("button",{onClick:t[2]||(t[2]=a=>g.value=!0),class:"px-4 py-2 text-sm font-medium text-red-700 dark:text-red-300 bg-red-100 dark:bg-red-900/20 hover:bg-red-200 dark:hover:bg-red-900/30 rounded-lg transition-colors"}," 申请注销账号 ")])])]),e("div",$e,[e("button",{onClick:t[3]||(t[3]=a=>s.$router.go(-1)),class:"px-6 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 取消 "),e("button",{onClick:L,loading:p.value,class:"px-6 py-2 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"},r(u.value==="all"?"退出所有设备":"退出登录"),9,Se)])]),c(O,{modelValue:g.value,"onUpdate:modelValue":t[7]||(t[7]=a=>g.value=a),title:"注销账号确认",width:"500px"},{footer:v(()=>[e("div",He,[c(V,{onClick:t[6]||(t[6]=a=>g.value=!1)},{default:v(()=>t[22]||(t[22]=[P("取消")])),_:1,__:[22]}),c(V,{type:"danger",onClick:F,loading:f.value,disabled:!C.value},{default:v(()=>t[23]||(t[23]=[P(" 确认注销账号 ")])),_:1,__:[23]},8,["loading","disabled"])])]),default:v(()=>[e("div",Pe,[e("div",Ue,[c(j(D),{class:"w-6 h-6 text-red-600 dark:text-red-400 mt-0.5"}),t[19]||(t[19]=e("div",null,[e("h3",{class:"font-medium text-gray-900 dark:text-dark-text mb-2"},"确认注销账号"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mb-4"}," 注销账号后，您将失去以下数据： "),e("ul",{class:"text-sm text-gray-600 dark:text-dark-text-secondary space-y-1 mb-4"},[e("li",null,"• 所有图库文件和处理结果"),e("li",null,"• 商品信息和SKU数据"),e("li",null,"• 交易记录和充值记录"),e("li",null,"• 工作流配置和历史记录"),e("li",null,"• 子账号和权限设置")]),e("p",{class:"text-sm text-red-600 dark:text-red-400 font-medium"}," 此操作不可恢复，请谨慎操作！ ")],-1))]),e("div",null,[t[20]||(t[20]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 请输入您的密码以确认注销 ",-1)),c(n,{modelValue:y.value,"onUpdate:modelValue":t[4]||(t[4]=a=>y.value=a),type:"password",placeholder:"请输入当前密码","show-password":""},null,8,["modelValue"])]),e("div",null,[t[21]||(t[21]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"},' 请输入 "确认注销" 以继续 ',-1)),c(n,{modelValue:b.value,"onUpdate:modelValue":t[5]||(t[5]=a=>b.value=a),placeholder:"请输入：确认注销"},null,8,["modelValue"])])])]),_:1},8,["modelValue"])])}}});export{ze as default};
