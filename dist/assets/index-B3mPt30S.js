import{d as Z,r as a,e as F,c as u,a as t,g as s,p as ee,t as d,M as x,j as C,h as v,C as M,F as S,k as te,E as z,n as B,w as U,o as c,_ as oe}from"./index-D_iwS02E.js";import{I as re}from"./ImagePreviewDialog-DZn9Pzr_.js";const ae={class:"space-y-6"},se={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},le={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ne={class:"flex items-center justify-between"},de={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ie={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ue={class:"flex items-center justify-between"},ce={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},ge={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},pe={class:"flex items-center justify-between"},me={class:"text-2xl font-bold text-purple-600 dark:text-purple-400"},xe={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ve={class:"flex items-center justify-between"},he={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ke={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},be={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},fe={class:"flex items-center space-x-3"},we={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},ye={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},_e={class:"flex items-center space-x-3"},Ce={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},je={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Me={class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},ze={class:"p-6"},Be={class:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4"},Le=["onClick"],Ve={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-green-500 rounded-full flex items-center justify-center z-10"},Te={class:"relative"},Fe={key:0,class:"absolute inset-0 checkerboard-bg"},Se=["src","alt"],Ue={class:"absolute top-2 left-2 flex flex-col space-y-1"},De={key:0,class:"absolute bottom-2 left-2"},Ke={class:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center"},Ae={class:"opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex space-x-2"},Ie=["onClick"],Ne=["onClick"],Pe={class:"mt-2"},$e=["title"],Ee={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Re={key:0,class:"flex justify-center mt-6"},Ge=Z({__name:"index",setup(He){const D=a(),K=a([]),i=a([]),h=a(""),k=a(""),b=a(""),f=a(1),m=a(24),j=a(!1),L=a(null),A=a(324),I=a(156),N=a(89),P=a(234),p=a([{id:"mat_001",name:"星星图标.svg",thumbnail:"https://picsum.photos/300/300?random=11",url:"https://picsum.photos/800/800?random=11",category:"icon",format:"svg",size:"12KB",uploadTime:"2024-01-15 14:30",hasTransparency:!0,tags:["星星","图标","装饰"]},{id:"mat_002",name:"花朵装饰.png",thumbnail:"https://picsum.photos/300/300?random=12",url:"https://picsum.photos/800/800?random=12",category:"decoration",format:"png",size:"45KB",uploadTime:"2024-01-15 14:25",hasTransparency:!0,tags:["花朵","装饰","自然"]},{id:"mat_003",name:"金色边框.png",thumbnail:"https://picsum.photos/300/300?random=13",url:"https://picsum.photos/800/800?random=13",category:"border",format:"png",size:"78KB",uploadTime:"2024-01-15 14:20",hasTransparency:!0,tags:["边框","金色","装饰"]},{id:"mat_004",name:"木纹纹理.jpg",thumbnail:"https://picsum.photos/300/300?random=14",url:"https://picsum.photos/800/800?random=14",category:"texture",format:"jpg",size:"234KB",uploadTime:"2024-01-15 13:45",hasTransparency:!1,tags:["木纹","纹理","背景"]},{id:"mat_005",name:"爱心图标.svg",thumbnail:"https://picsum.photos/300/300?random=15",url:"https://picsum.photos/800/800?random=15",category:"icon",format:"svg",size:"8KB",uploadTime:"2024-01-15 13:40",hasTransparency:!0,tags:["爱心","图标","情感"]},{id:"mat_006",name:"蝴蝶装饰.png",thumbnail:"https://picsum.photos/300/300?random=16",url:"https://picsum.photos/800/800?random=16",category:"decoration",format:"png",size:"56KB",uploadTime:"2024-01-15 13:35",hasTransparency:!0,tags:["蝴蝶","装饰","动物"]}]),w=F(()=>{let o=p.value;if(k.value&&(o=o.filter(e=>e.category===k.value)),b.value&&(o=o.filter(e=>e.format===b.value)),h.value){const e=h.value.toLowerCase();o=o.filter(n=>{var l;return n.name.toLowerCase().includes(e)||((l=n.tags)==null?void 0:l.some(g=>g.toLowerCase().includes(e)))})}return o}),$=F(()=>{const o=(f.value-1)*m.value,e=o+m.value;return w.value.slice(o,e)}),E=o=>{if(o.raw){const e=new FileReader;e.onload=n=>{var g,y,_;const l={id:"mat_"+Date.now(),name:o.name,thumbnail:(g=n.target)==null?void 0:g.result,url:(y=n.target)==null?void 0:y.result,category:"icon",format:(_=o.name.split(".").pop())==null?void 0:_.toLowerCase(),size:G(o.size||0),uploadTime:new Date().toLocaleString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),hasTransparency:o.name.toLowerCase().includes(".png")||o.name.toLowerCase().includes(".svg"),tags:[]};p.value.unshift(l),z.success(`素材 ${o.name} 上传成功`)},e.readAsDataURL(o.raw)}},R=o=>{const e=p.value.findIndex(n=>n.name===o.name);e>-1&&p.value.splice(e,1)},G=o=>o<1024?o+" B":o<1024*1024?(o/1024).toFixed(1)+" KB":(o/(1024*1024)).toFixed(1)+" MB",V=o=>i.value.some(e=>e.id===o.id),H=o=>{const e=i.value.findIndex(n=>n.id===o.id);e>-1?i.value.splice(e,1):i.value.push(o)},J=o=>({icon:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300",decoration:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",border:"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300",texture:"bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",q=o=>({icon:"图标",decoration:"装饰",border:"边框",texture:"纹理"})[o]||"未知",O=o=>({svg:"bg-indigo-100 text-indigo-800 dark:bg-indigo-900/30 dark:text-indigo-300",png:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300",jpg:"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300"})[o]||"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300",Q=o=>{L.value=o,j.value=!0},W=o=>{const e=document.createElement("a");e.href=o.url,e.download=o.name,document.body.appendChild(e),e.click(),document.body.removeChild(e),z.success(`正在下载 ${o.name}`)},X=()=>{i.value.length!==0&&(i.value.forEach(o=>{const e=p.value.findIndex(n=>n.id===o.id);e>-1&&p.value.splice(e,1)}),z.success(`已删除 ${i.value.length} 个素材`),i.value=[])},Y=()=>{f.value=1};return(o,e)=>{const n=v("el-upload"),l=v("el-option"),g=v("el-select"),y=v("el-input"),_=v("el-pagination");return c(),u(S,null,[t("div",ae,[e[22]||(e[22]=ee('<div class="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-2xl p-6 border border-green-100 dark:border-green-800" data-v-eb354393><div class="flex items-center space-x-3" data-v-eb354393><div class="w-10 h-10 bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center" data-v-eb354393><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-eb354393><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" data-v-eb354393></path></svg></div><div data-v-eb354393><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-eb354393>素材图库</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-eb354393>管理设计素材和创意元素</p></div></div></div>',1)),t("div",se,[t("div",le,[t("div",ne,[t("div",null,[e[6]||(e[6]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"素材总数",-1)),t("p",de,d(A.value),1)]),e[7]||(e[7]=t("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z"})])],-1))])]),t("div",ie,[t("div",ue,[t("div",null,[e[8]||(e[8]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"图标数量",-1)),t("p",ce,d(I.value),1)]),e[9]||(e[9]=t("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1))])]),t("div",ge,[t("div",pe,[t("div",null,[e[10]||(e[10]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"装饰元素",-1)),t("p",me,d(N.value),1)]),e[11]||(e[11]=t("div",{class:"w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-purple-600 dark:text-purple-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"})])],-1))])]),t("div",xe,[t("div",ve,[t("div",null,[e[12]||(e[12]=t("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"透明素材",-1)),t("p",he,d(P.value),1)]),e[13]||(e[13]=t("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[t("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])],-1))])])]),t("div",ke,[t("div",be,[t("div",fe,[s(n,{ref_key:"uploadRef",ref:D,"file-list":K.value,"on-change":E,"on-remove":R,"auto-upload":!1,accept:"image/*,.svg",multiple:"","show-file-list":!1},{default:C(()=>e[14]||(e[14]=[t("button",{class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[t("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),M(" 上传素材 ")],-1)])),_:1,__:[14]},8,["file-list"]),i.value.length>0?(c(),u("div",we,[t("span",ye," 已选择 "+d(i.value.length)+" 个素材 ",1),t("button",{onClick:X,class:"inline-flex items-center px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},e[15]||(e[15]=[t("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})],-1),M(" 批量删除 ")]))])):x("",!0)]),t("div",_e,[s(g,{modelValue:k.value,"onUpdate:modelValue":e[0]||(e[0]=r=>k.value=r),placeholder:"素材分类",style:{width:"120px"},clearable:""},{default:C(()=>[s(l,{label:"全部",value:""}),s(l,{label:"图标",value:"icon"}),s(l,{label:"装饰",value:"decoration"}),s(l,{label:"边框",value:"border"}),s(l,{label:"纹理",value:"texture"})]),_:1},8,["modelValue"]),s(g,{modelValue:b.value,"onUpdate:modelValue":e[1]||(e[1]=r=>b.value=r),placeholder:"文件格式",style:{width:"100px"},clearable:""},{default:C(()=>[s(l,{label:"全部",value:""}),s(l,{label:"PNG",value:"png"}),s(l,{label:"SVG",value:"svg"}),s(l,{label:"JPG",value:"jpg"})]),_:1},8,["modelValue"]),s(y,{modelValue:h.value,"onUpdate:modelValue":e[2]||(e[2]=r=>h.value=r),placeholder:"搜索素材...",style:{width:"200px"},clearable:"",onInput:Y},{prefix:C(()=>e[16]||(e[16]=[t("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"])])])]),t("div",Ce,[t("div",je,[e[17]||(e[17]=t("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"设计素材",-1)),t("p",Me,"共 "+d(w.value.length)+" 个素材",1)]),t("div",ze,[t("div",Be,[(c(!0),u(S,null,te($.value,r=>(c(),u("div",{key:r.id,class:"group relative cursor-pointer",onClick:T=>H(r)},[V(r)?(c(),u("div",Ve,e[18]||(e[18]=[t("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):x("",!0),t("div",Te,[t("div",{class:B(["w-full h-32 rounded-lg border-2 transition-all duration-200 overflow-hidden",V(r)?"border-green-500":"border-gray-200 dark:border-dark-border group-hover:border-green-300"])},[r.hasTransparency?(c(),u("div",Fe)):x("",!0),t("img",{src:r.thumbnail,alt:r.name,class:"w-full h-full object-contain relative z-10"},null,8,Se)],2),t("div",Ue,[t("span",{class:B([J(r.category),"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"])},d(q(r.category)),3),t("span",{class:B([O(r.format),"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium"])},d(r.format.toUpperCase()),3)]),r.hasTransparency?(c(),u("div",De,e[19]||(e[19]=[t("span",{class:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300"},[t("svg",{class:"w-3 h-3 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),M(" 透明 ")],-1)]))):x("",!0),t("div",Ke,[t("div",Ae,[t("button",{onClick:U(T=>Q(r),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},e[20]||(e[20]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})],-1)]),8,Ie),t("button",{onClick:U(T=>W(r),["stop"]),class:"p-2 bg-white rounded-full shadow-lg hover:shadow-xl transition-all duration-200"},e[21]||(e[21]=[t("svg",{class:"w-4 h-4 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[t("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1)]),8,Ne)])])]),t("div",Pe,[t("p",{class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate",title:r.name},d(r.name),9,$e),t("p",Ee,d(r.size)+" • "+d(r.uploadTime),1)])],8,Le))),128))]),w.value.length>m.value?(c(),u("div",Re,[s(_,{"current-page":f.value,"onUpdate:currentPage":e[3]||(e[3]=r=>f.value=r),"page-size":m.value,"onUpdate:pageSize":e[4]||(e[4]=r=>m.value=r),"page-sizes":[24,48,96],total:w.value.length,layout:"sizes, prev, pager, next",class:"modern-pagination"},null,8,["current-page","page-size","total"])])):x("",!0)])])]),s(re,{modelValue:j.value,"onUpdate:modelValue":e[5]||(e[5]=r=>j.value=r),image:L.value},null,8,["modelValue","image"])],64)}}}),Oe=oe(Ge,[["__scopeId","data-v-eb354393"]]);export{Oe as default};
