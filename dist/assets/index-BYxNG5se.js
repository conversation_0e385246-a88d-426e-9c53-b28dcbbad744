import{d as Q,e as E,r as x,q as O,j as n,h as w,a as e,g as s,C,o as k,E as f,_ as J,c as h,F as W,M as q,k as X,t as d,b as ee,Q as te,f as le,p as ne,A as K,G as de,n as ie}from"./index-gGKuRSZs.js";import{_ as ue}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-I6oFGs3s.js";import{r as Z}from"./ArrowDownTrayIcon-DUfu9sNO.js";const ce={class:"space-y-6"},me={class:"space-y-2"},ge={class:"space-y-2"},xe={class:"space-y-2"},pe={class:"flex justify-end space-x-3"},ke=Q({__name:"CreatePresetDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(P,{emit:B}){const z=P,$=B,_=E({get:()=>z.modelValue,set:V=>$("update:modelValue",V)}),m=x({name:"",description:"",rules:""}),T=()=>{if(!m.value.name.trim()){f.warning("请输入预设名称");return}if(!m.value.rules.trim()){f.warning("请输入生成规则");return}const V={id:Date.now().toString(),name:m.value.name.trim(),description:m.value.description.trim(),rules:m.value.rules.trim()};$("success",V),$("update:modelValue",!1),D()},D=()=>{m.value={name:"",description:"",rules:""}};return(V,u)=>{const l=w("el-input"),y=w("el-button"),M=w("el-dialog");return k(),O(M,{modelValue:_.value,"onUpdate:modelValue":u[4]||(u[4]=v=>_.value=v),title:"新建预设",width:"600px","align-center":"",onClose:D},{footer:n(()=>[e("div",pe,[s(y,{onClick:u[3]||(u[3]=v=>_.value=!1)},{default:n(()=>u[9]||(u[9]=[C("取消")])),_:1,__:[9]}),s(y,{type:"primary",onClick:T,disabled:!m.value.name.trim()||!m.value.rules.trim()},{default:n(()=>u[10]||(u[10]=[C(" 创建预设 ")])),_:1,__:[10]},8,["disabled"])])]),default:n(()=>[e("div",ce,[e("div",me,[u[5]||(u[5]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"},[C(" 预设名称 "),e("span",{class:"text-red-500"},"*")],-1)),s(l,{modelValue:m.value.name,"onUpdate:modelValue":u[0]||(u[0]=v=>m.value.name=v),placeholder:"请输入预设名称，如：亚马逊标题",class:"modern-input"},null,8,["modelValue"])]),e("div",ge,[u[6]||(u[6]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"}," 预设描述 ",-1)),s(l,{modelValue:m.value.description,"onUpdate:modelValue":u[1]||(u[1]=v=>m.value.description=v),placeholder:"请输入预设描述，简要说明此预设的用途和特点",class:"modern-input"},null,8,["modelValue"])]),e("div",xe,[u[7]||(u[7]=e("label",{class:"block text-sm font-medium text-gray-900 dark:text-dark-text"},[C(" 生成规则 "),e("span",{class:"text-red-500"},"*")],-1)),s(l,{modelValue:m.value.rules,"onUpdate:modelValue":u[2]||(u[2]=v=>m.value.rules=v),type:"textarea",rows:8,placeholder:"请输入详细的生成规则...",class:"modern-textarea"},null,8,["modelValue"]),u[8]||(u[8]=e("div",{class:"mt-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"},[e("h4",{class:"text-sm font-medium text-blue-900 dark:text-blue-100 mb-2"},"预设示例："),e("div",{class:"text-sm text-blue-800 dark:text-blue-200 space-y-2"},[e("p",null,[e("strong",null,"【亚马逊标题】")]),e("p",null,"任务：根据图片内容拟定标题；"),e("p",null,"要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符")])],-1))])])]),_:1},8,["modelValue"])}}}),ve=J(ke,[["__scopeId","data-v-34a6ef34"]]),be={class:"space-y-6"},he={class:"flex justify-start space-x-3"},fe={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},we={class:"flex items-center justify-between mb-3"},ye={class:"relative"},_e={key:0,class:"absolute right-0 top-8 w-80 bg-white dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-lg shadow-lg z-10"},Ce={class:"p-4"},$e={class:"flex items-center justify-between mb-3"},Ve={class:"space-y-2 max-h-48 overflow-y-auto"},je=["onClick"],Te={class:"font-medium text-sm text-gray-900 dark:text-dark-text"},Me={class:"text-xs text-gray-500 dark:text-dark-text-secondary mt-1 line-clamp-2"},Se={key:0,class:"space-y-4"},Be={class:"flex items-center justify-between"},ze={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},De={class:"text-xs text-gray-500 dark:text-dark-text-secondary"},Le={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4 max-h-80 overflow-y-auto"},Pe={class:"grid grid-cols-6 gap-4"},He={class:"relative"},Ge=["src","alt"],Ue=["onClick"],Ie={class:"text-xs text-gray-600 dark:text-dark-text-secondary mt-1 truncate"},Ne={key:0,class:"mt-4 flex justify-center"},Fe={key:1,class:"text-center py-12 border-2 border-dashed border-gray-200 dark:border-dark-border rounded-lg"},Ae={class:"flex justify-between items-center"},Re={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},We={class:"flex space-x-3"},Ee=Q({__name:"CreateTitleDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(P,{emit:B}){const z=P,$=B,_=E({get:()=>z.modelValue,set:g=>$("update:modelValue",g)}),m=x(!1),T=x(!1),D=x(!1),V=x(!1),u=x([]),l=x([]),y=x(1),M=x(18),v=x(""),H=x([{id:"1",name:"亚马逊标题",description:"根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循亚马逊平台要求，输出英文，总字符量不超过120个字符",rules:"任务：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符"},{id:"2",name:"Temu标题",description:"根据图片内容生成Temu平台标题；要求：突出产品特色和卖点，中英文混合，字符数80-100",rules:"任务：根据图片内容拟定标题；要求：突出产品特色和卖点，遵循<Temu>平台要求，输出<中英文混合>，总字符量80-100个字符"},{id:"3",name:"Shein标题",description:"根据图片内容生成Shein平台标题；要求：时尚感强，突出风格和场合，英文输出，60-80字符",rules:"任务：根据图片内容拟定标题；要求：时尚感强，突出风格和场合，遵循<Shein>平台要求，输出<英文>，总字符量60-80个字符"}]),b=E(()=>{const g=(y.value-1)*M.value,r=g+M.value;return l.value.slice(g,r)}),o=g=>{const r=new FileReader;r.onload=j=>{var G;const R=Date.now()+Math.random();l.value.push({id:R,name:g.name,url:(G=j.target)==null?void 0:G.result,file:g.raw})},r.readAsDataURL(g.raw)},S=g=>{},U=g=>{l.value.splice(g,1),b.value.length===0&&y.value>1&&y.value--},I=g=>{const r=g.map(j=>({id:j.id,name:j.name,url:j.url}));l.value.push(...r),m.value=!1,f.success(`已添加 ${g.length} 张图片`)},N=g=>{v.value=g.rules,T.value=!1,f.success(`已应用预设：${g.name}`)},F=g=>{H.value.push(g),f.success("预设创建成功！")},A=()=>{if(l.value.length===0){f.warning("请先选择图片");return}if(!v.value.trim()){f.warning("请设置生成规则");return}f.success(`正在创建标题生成任务，共 ${l.value.length} 张图片`),p(),$("success"),$("update:modelValue",!1)},p=()=>{l.value=[],u.value=[],y.value=1,v.value="",T.value=!1};return(g,r)=>{const j=w("el-button"),R=w("el-upload"),G=w("el-input"),c=w("el-pagination"),t=w("el-dialog");return k(),h(W,null,[s(t,{modelValue:_.value,"onUpdate:modelValue":r[8]||(r[8]=a=>_.value=a),title:"新建标题生成任务",width:"900px","align-center":"",onClose:p},{footer:n(()=>[e("div",Ae,[e("div",Re,d(l.value.length>0?`将处理 ${l.value.length} 张图片，生成 ${l.value.length*3} 个标题`:"请先选择图片和设置生成规则"),1),e("div",We,[s(j,{onClick:r[7]||(r[7]=a=>_.value=!1)},{default:n(()=>r[18]||(r[18]=[C("取消")])),_:1,__:[18]}),s(j,{type:"primary",onClick:A,disabled:l.value.length===0||!v.value.trim()},{default:n(()=>r[19]||(r[19]=[C(" 提交任务 ")])),_:1,__:[19]},8,["disabled"])])])]),default:n(()=>[e("div",be,[e("div",he,[s(R,{ref:"uploadRef","file-list":u.value,"on-change":o,"on-remove":S,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:n(()=>[s(j,{type:"primary",size:"large"},{default:n(()=>r[11]||(r[11]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),C(" 上传图片 ")])),_:1,__:[11]})]),_:1},8,["file-list"]),s(j,{size:"large",onClick:r[0]||(r[0]=a=>m.value=!0)},{default:n(()=>r[12]||(r[12]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),C(" 从图库选择 ")])),_:1,__:[12]})]),e("div",fe,[e("div",we,[r[14]||(r[14]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"生成规则设置",-1)),e("div",ye,[e("button",{onMouseenter:r[1]||(r[1]=a=>D.value=!0),onMouseleave:r[2]||(r[2]=a=>D.value=!1),onClick:r[3]||(r[3]=a=>T.value=!T.value),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200"}," 使用预设 ",32),T.value?(k(),h("div",_e,[e("div",Ce,[e("div",$e,[r[13]||(r[13]=e("h5",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"预设列表",-1)),e("button",{onClick:r[4]||(r[4]=a=>V.value=!0),class:"text-xs text-blue-600 dark:text-blue-400 hover:text-blue-700"}," 新建预设 ")]),e("div",Ve,[(k(!0),h(W,null,X(H.value,a=>(k(),h("div",{key:a.id,onClick:L=>N(a),class:"p-3 border border-gray-200 dark:border-dark-border rounded-lg cursor-pointer hover:bg-gray-50 dark:hover:bg-dark-card transition-colors duration-200"},[e("div",Te,d(a.name),1),e("div",Me,d(a.description),1)],8,je))),128))])])])):q("",!0)])]),s(G,{modelValue:v.value,"onUpdate:modelValue":r[5]||(r[5]=a=>v.value=a),type:"textarea",rows:6,placeholder:"请输入生成规则，例如：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循亚马逊平台要求，输出英文，总字符量不超过120个字符",class:"modern-textarea"},null,8,["modelValue"])]),l.value.length>0?(k(),h("div",Se,[e("div",Be,[e("h4",ze," 已选择图片 ("+d(l.value.length)+") ",1),e("div",De," 预计生成 "+d(l.value.length*3)+" 个标题 ",1)]),e("div",Le,[e("div",Pe,[(k(!0),h(W,null,X(b.value,(a,L)=>(k(),h("div",{key:a.id||L,class:"relative group"},[e("div",He,[e("img",{src:a.url,alt:a.name,class:"w-full h-20 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Ge),e("button",{onClick:Y=>U(L+(y.value-1)*M.value),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200 hover:bg-red-600"},r[15]||(r[15]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ue),r[16]||(r[16]=e("div",{class:"absolute bottom-1 right-1 bg-blue-500 text-white text-xs px-1.5 py-0.5 rounded"}," ×3 ",-1))]),e("p",Ie,d(a.name),1)]))),128))]),l.value.length>M.value?(k(),h("div",Ne,[s(c,{"current-page":y.value,"onUpdate:currentPage":r[6]||(r[6]=a=>y.value=a),"page-size":M.value,total:l.value.length,layout:"prev, pager, next",small:""},null,8,["current-page","page-size","total"])])):q("",!0)])])):(k(),h("div",Fe,r[17]||(r[17]=[e("svg",{class:"w-12 h-12 text-gray-400 dark:text-dark-text-secondary mx-auto mb-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("p",{class:"text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库选择",-1)])))])]),_:1},8,["modelValue"]),s(ue,{modelValue:m.value,"onUpdate:modelValue":r[9]||(r[9]=a=>m.value=a),"theme-color":"blue",onSelect:I},null,8,["modelValue"]),s(ve,{modelValue:V.value,"onUpdate:modelValue":r[10]||(r[10]=a=>V.value=a),onSuccess:F},null,8,["modelValue"])],64)}}}),Oe=J(Ee,[["__scopeId","data-v-75f92d91"]]),qe={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},Qe={class:"flex items-center space-x-3"},Je={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ke={key:0,class:"p-6 grid grid-cols-2 md:grid-cols-4 gap-4"},Xe={class:"bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900/20 dark:to-blue-800/20 p-4 rounded-xl border border-blue-200 dark:border-blue-800"},Ye={class:"flex items-center space-x-2"},Ze={class:"text-sm font-bold text-blue-900 dark:text-blue-100"},et={class:"bg-gradient-to-br from-green-50 to-green-100 dark:from-green-900/20 dark:to-green-800/20 p-4 rounded-xl border border-green-200 dark:border-green-800"},tt={class:"flex items-center space-x-2"},rt={class:"text-sm font-bold text-green-900 dark:text-green-100"},st={class:"bg-gradient-to-br from-indigo-50 to-indigo-100 dark:from-indigo-900/20 dark:to-indigo-800/20 p-4 rounded-xl border border-indigo-200 dark:border-indigo-800"},ot={class:"flex items-center space-x-2"},at={class:"text-sm font-bold text-indigo-900 dark:text-indigo-100"},lt={class:"bg-gradient-to-br from-cyan-50 to-cyan-100 dark:from-cyan-900/20 dark:to-cyan-800/20 p-4 rounded-xl border border-cyan-200 dark:border-cyan-800"},nt={class:"flex items-center space-x-2"},dt={class:"text-sm font-bold text-cyan-900 dark:text-cyan-100"},it={class:"px-6 pb-4"},ut={class:"bg-gray-50 dark:bg-dark-card/50 p-4 rounded-lg border border-gray-100 dark:border-dark-border"},ct={class:"text-sm text-gray-700 dark:text-dark-text-secondary whitespace-pre-line"},mt={class:"px-6 pb-6"},gt={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},xt={key:0,class:"flex justify-center"},pt={key:1,class:"flex justify-center"},kt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},vt={key:0,class:"space-y-2"},bt={class:"flex items-center justify-between"},ht={class:"text-sm text-gray-900 dark:text-dark-text"},ft=["onClick"],wt={key:1,class:"text-sm text-gray-500 dark:text-dark-text-secondary"},yt={key:0,class:"flex justify-center"},_t=["onClick"],Ct={key:1,class:"flex justify-center"},$t={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Vt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},jt=Q({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(P,{emit:B}){const z=P,$=B,_=E({get:()=>z.modelValue,set:b=>$("update:modelValue",b)}),m=x(!1),T=x(0),D=x("任务：根据图片内容拟定标题；要求：商品款式+商品颜色+印花图案+使用场景，遵循<亚马逊>平台要求，输出<英文>，总字符量不超过120个字符"),V=x([{index:1,image:"https://picsum.photos/400/400?random=1",fileName:"product_001.jpg",titles:["Women's Floral Print Summer Dress - Blue Casual Maxi Dress with Pockets for Beach Vacation","Elegant Blue Floral Maxi Dress for Women - Casual Summer Beach Vacation Outfit with Side Pockets","Summer Beach Maxi Dress - Women's Blue Floral Print Casual Outfit with Pockets for Vacation"],status:"success"},{index:2,image:"https://picsum.photos/400/400?random=2",fileName:"product_002.jpg",titles:["Men's Casual Linen Shirt - White Button Down Short Sleeve Beach Shirt for Summer Vacation","Summer White Linen Shirt for Men - Casual Short Sleeve Button Down Beach Wear for Vacation","Men's White Short Sleeve Linen Shirt - Casual Button Down Beach Wear for Summer Vacation"],status:"success"},{index:3,image:"https://picsum.photos/400/400?random=3",fileName:"product_003.jpg",titles:[],status:"failed"}]);T.value=V.value.length;const u=b=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[b]||"未知",l=b=>({success:"成功",failed:"失败"})[b]||"未知",y=()=>{_.value=!1},M=b=>{navigator.clipboard.writeText(b).then(()=>{f.success("标题已复制到剪贴板")}).catch(()=>{f.error("复制失败，请手动复制")})},v=b=>{f.success(`正在导出 ${b.fileName} 的标题`)},H=()=>{f.success("正在导出所有标题...")};return(b,o)=>{const S=w("el-table-column"),U=w("el-image"),I=w("el-tag"),N=w("el-table"),F=w("el-dialog"),A=te("loading");return k(),O(F,{modelValue:_.value,"onUpdate:modelValue":o[0]||(o[0]=p=>_.value=p),width:"1200px","before-close":y,"show-close":!1,class:"modern-dialog"},{header:n(()=>{var p;return[e("div",qe,[e("div",Qe,[o[2]||(o[2]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1)),e("div",null,[o[1]||(o[1]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"标题生成详情",-1)),e("p",Je,"任务ID: "+d(((p=b.task)==null?void 0:p.id)||""),1)])]),e("button",{onClick:y,class:"p-2 text-gray-400 hover:text-gray-600 dark:text-dark-text-secondary dark:hover:text-dark-text rounded-lg hover:bg-gray-100 dark:hover:bg-dark-card transition-all duration-200"},o[3]||(o[3]=[e("svg",{class:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:n(()=>[e("div",$t,[e("div",Vt," 共 "+d(T.value)+" 条生成结果 ",1),e("div",{class:"flex items-center space-x-3"},[e("button",{onClick:y,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),e("button",{onClick:H,class:"inline-flex items-center px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},o[17]||(o[17]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),C(" 导出全部 ")]))])])]),default:n(()=>[b.task?(k(),h("div",Ke,[e("div",Xe,[e("div",Ye,[o[5]||(o[5]=e("div",{class:"w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1)),e("div",null,[o[4]||(o[4]=e("p",{class:"text-xs text-blue-600 dark:text-blue-400 font-medium"},"任务状态",-1)),e("p",Ze,d(u(b.task.status)),1)])])]),e("div",et,[e("div",tt,[o[7]||(o[7]=e("div",{class:"w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)),e("div",null,[o[6]||(o[6]=e("p",{class:"text-xs text-green-600 dark:text-green-400 font-medium"},"图片数量",-1)),e("p",rt,d(b.task.imageCount),1)])])]),e("div",st,[e("div",ot,[o[9]||(o[9]=e("div",{class:"w-8 h-8 bg-indigo-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1)),e("div",null,[o[8]||(o[8]=e("p",{class:"text-xs text-indigo-600 dark:text-indigo-400 font-medium"},"生成数量",-1)),e("p",at,d(b.task.generatedCount),1)])])]),e("div",lt,[e("div",nt,[o[11]||(o[11]=e("div",{class:"w-8 h-8 bg-cyan-500 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"})])],-1)),e("div",null,[o[10]||(o[10]=e("p",{class:"text-xs text-cyan-600 dark:text-cyan-400 font-medium"},"使用预设",-1)),e("p",dt,d(b.task.preset||"自定义规则"),1)])])])])):q("",!0),e("div",it,[e("div",ut,[o[12]||(o[12]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-2"},"生成规则",-1)),e("p",ct,d(D.value),1)])]),e("div",mt,[o[16]||(o[16]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"标题生成结果",-1)),e("div",gt,[ee((k(),O(N,{data:V.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:n(()=>[s(S,{prop:"index",label:"序号",width:"80",align:"center"}),s(S,{label:"图片",width:"100",align:"center"},{default:n(p=>[p.row.status==="success"?(k(),h("div",xt,[s(U,{src:p.row.image,"preview-src-list":[p.row.image],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border","preview-teleported":!0},null,8,["src","preview-src-list"])])):(k(),h("div",pt,o[13]||(o[13]=[e("div",{class:"w-16 h-16 bg-gray-100 dark:bg-dark-card rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1)])))]),_:1}),s(S,{prop:"fileName",label:"文件名",width:"150",align:"center"},{default:n(p=>[e("span",kt,d(p.row.fileName),1)]),_:1}),s(S,{prop:"title",label:"生成标题","min-width":"300"},{default:n(p=>[p.row.status==="success"?(k(),h("div",vt,[(k(!0),h(W,null,X(p.row.titles,(g,r)=>(k(),h("div",{key:r,class:"p-2 bg-gray-50 dark:bg-dark-card rounded border border-gray-200 dark:border-dark-border"},[e("div",bt,[e("span",ht,d(g),1),e("button",{onClick:j=>M(g),class:"text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300"},o[14]||(o[14]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 5H6a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2v-2M8 5a2 2 0 002 2h2a2 2 0 002-2M8 5a2 2 0 012-2h2a2 2 0 012 2m0 0h2a2 2 0 012 2v3m2 4H10m0 0l3-3m-3 3l3 3"})],-1)]),8,ft)])]))),128))])):(k(),h("div",wt,d(l(p.row.status)),1))]),_:1}),s(S,{prop:"status",label:"状态",width:"100",align:"center"},{default:n(p=>[s(I,{type:p.row.status==="success"?"success":"danger",size:"small"},{default:n(()=>[C(d(l(p.row.status)),1)]),_:2},1032,["type"])]),_:1}),s(S,{label:"操作",width:"120",align:"center"},{default:n(p=>[p.row.status==="success"?(k(),h("div",yt,[e("button",{onClick:g=>v(p.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 bg-blue-50 dark:bg-blue-900/30 rounded-lg hover:bg-blue-100 dark:hover:bg-blue-900/50 transition-all duration-200"}," 导出 ",8,_t)])):(k(),h("div",Ct,o[15]||(o[15]=[e("span",{class:"text-sm text-gray-400"},"-",-1)])))]),_:1})]),_:1},8,["data"])),[[A,m.value]])])])]),_:1},8,["modelValue"])}}}),Tt=J(jt,[["__scopeId","data-v-752f1d1d"]]),Mt={class:"space-y-6"},St={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Bt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},zt={class:"flex items-center justify-between"},Dt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Lt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Pt={class:"flex items-center justify-between"},Ht={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Gt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ut={class:"flex items-center justify-between"},It={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},Nt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Ft={class:"flex items-center justify-between"},At={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Rt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},Wt={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},Et={class:"flex items-center space-x-3"},Ot={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},qt={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Qt={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Jt={class:"overflow-x-auto"},Kt={class:"font-mono text-sm text-gray-600 dark:text-dark-text-secondary"},Xt={class:"flex items-center space-x-2"},Yt={class:"font-medium text-gray-900 dark:text-dark-text"},Zt={class:"font-medium text-blue-600 dark:text-blue-400"},er={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},tr={class:"flex items-center space-x-2"},rr={class:"w-6 h-6 bg-gradient-to-br from-blue-400 to-blue-500 rounded-full flex items-center justify-center"},sr={class:"text-white text-xs font-medium"},or={class:"text-sm text-gray-900 dark:text-dark-text"},ar={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},lr={class:"flex items-center space-x-2"},nr=["onClick"],dr={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},ir={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ur=Q({__name:"index",setup(P){const B=x(!1),z=x(!1),$=x(!1),_=x(null),m=x([]),T=x(2856),D=x(96.8),V=x(234),u=x(12),l=x({currentPage:1,pageSize:20,total:0}),y=x([{id:"TG001",imageCount:25,generatedCount:75,status:"completed",preset:"亚马逊标题",operator:"张三",createTime:"2024-01-15 14:30:25"},{id:"TG002",imageCount:18,generatedCount:54,status:"processing",preset:"Temu标题",operator:"李四",createTime:"2024-01-15 13:45:12"},{id:"TG003",imageCount:32,generatedCount:96,status:"completed",preset:"Shein标题",operator:"王五",createTime:"2024-01-15 12:20:08"},{id:"TG004",imageCount:15,generatedCount:0,status:"failed",preset:"自定义规则",operator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"TG005",imageCount:28,generatedCount:0,status:"pending",preset:"亚马逊标题",operator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"TG006",imageCount:22,generatedCount:66,status:"completed",preset:"eBay标题",operator:"孙八",createTime:"2024-01-15 09:45:22"},{id:"TG007",imageCount:19,generatedCount:38,status:"processing",preset:"自定义规则",operator:"周九",createTime:"2024-01-15 08:20:15"},{id:"TG008",imageCount:35,generatedCount:105,status:"completed",preset:"亚马逊标题",operator:"吴十",createTime:"2024-01-14 16:30:40"},{id:"TG009",imageCount:12,generatedCount:36,status:"completed",preset:"Temu标题",operator:"郑一",createTime:"2024-01-14 15:15:28"},{id:"TG010",imageCount:26,generatedCount:52,status:"processing",preset:"Shein标题",operator:"王二",createTime:"2024-01-14 14:45:55"}]),M=E(()=>{const c=(l.value.currentPage-1)*l.value.pageSize,t=c+l.value.pageSize;return y.value.slice(c,t)});le(()=>{v()});const v=()=>{B.value=!0,setTimeout(()=>{l.value.total=y.value.length,B.value=!1},500)},H=c=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[c]||t.pending},b=c=>({completed:"已完成",processing:"处理中",failed:"失败",pending:"等待中"})[c]||"未知",o=c=>{m.value=c},S=c=>{_.value=c,$.value=!0},U=c=>{const{action:t,row:a}=c;switch(t){case"batchListing":A(a);break;case"smartCrop":p(a);break;case"oneClickCutout":g(a);break;case"superSplit":r(a);break;case"copyrightDetection":j(a);break}},I=()=>{f.success("导出表格功能开发中...")},N=()=>{f.success(`正在批量导出 ${m.value.length} 个任务...`)},F=()=>{f.success("标题生成任务创建成功！"),v()},A=c=>{f.success(`正在为标题生成任务 ${c.id} 创建批量刊登任务...`)},p=c=>{f.success(`正在为标题生成任务 ${c.id} 创建智能裁图任务...`)},g=c=>{f.success(`正在为标题生成任务 ${c.id} 创建一键抠图任务...`)},r=c=>{f.success(`正在为标题生成任务 ${c.id} 创建超级裂变任务...`)},j=c=>{f.success(`正在为标题生成任务 ${c.id} 创建侵权检测任务...`)},R=c=>{l.value.pageSize=c,l.value.currentPage=1,v()},G=c=>{l.value.currentPage=c,v()};return(c,t)=>{const a=w("el-table-column"),L=w("el-dropdown-item"),Y=w("el-dropdown-menu"),re=w("el-dropdown"),se=w("el-table"),oe=w("el-pagination"),ae=te("loading");return k(),h(W,null,[e("div",Mt,[t[26]||(t[26]=ne('<div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800" data-v-76e7460c><div class="flex items-center space-x-3" data-v-76e7460c><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" data-v-76e7460c><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-76e7460c><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" data-v-76e7460c></path></svg></div><div data-v-76e7460c><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-76e7460c>标题生成</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-76e7460c>AI智能生成吸引人的商品标题</p></div></div></div>',1)),e("div",St,[e("div",Bt,[e("div",zt,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"总生成数",-1)),e("p",Dt,d(T.value),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})])],-1))])]),e("div",Lt,[e("div",Pt,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Ht,d(D.value)+"%",1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Gt,[e("div",Ut,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日生成",-1)),e("p",It,d(V.value),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})])],-1))])]),e("div",Nt,[e("div",Ft,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中",-1)),e("p",At,d(u.value),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])])]),e("div",Rt,[e("div",Wt,[e("div",Et,[e("button",{onClick:t[0]||(t[0]=i=>z.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[s(K(de),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=C(" 新建生成 "))]),e("button",{onClick:I,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[s(K(Z),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=C(" 导出表格 "))]),m.value.length>0?(k(),h("div",Ot,[e("span",qt," 已选择 "+d(m.value.length)+" 项 ",1),e("button",{onClick:N,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[s(K(Z),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=C(" 批量导出 "))])])):q("",!0)])])]),e("div",Qt,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"标题生成任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有标题生成任务")],-1)),e("div",Jt,[ee((k(),O(se,{data:M.value,style:{width:"100%"},onSelectionChange:o,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:n(()=>[s(a,{type:"selection",width:"55"}),s(a,{prop:"id",label:"标题ID",width:"120"},{default:n(i=>[e("span",Kt,d(i.row.id),1)]),_:1}),s(a,{label:"生成数量",width:"150"},{default:n(i=>[e("div",Xt,[t[16]||(t[16]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"图片:",-1)),e("span",Yt,d(i.row.imageCount),1),t[17]||(t[17]=e("span",{class:"text-gray-400"},"|",-1)),t[18]||(t[18]=e("span",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"标题:",-1)),e("span",Zt,d(i.row.generatedCount),1)])]),_:1}),s(a,{prop:"status",label:"生成状态",width:"120"},{default:n(i=>[e("span",{class:ie([H(i.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},d(b(i.row.status)),3)]),_:1}),s(a,{prop:"preset",label:"使用预设",width:"150"},{default:n(i=>[e("span",er,d(i.row.preset||"自定义规则"),1)]),_:1}),s(a,{prop:"operator",label:"操作人",width:"100"},{default:n(i=>[e("div",tr,[e("div",rr,[e("span",sr,d(i.row.operator.charAt(0)),1)]),e("span",or,d(i.row.operator),1)])]),_:1}),s(a,{prop:"createTime",label:"创建时间",width:"180"},{default:n(i=>[e("div",ar,d(i.row.createTime),1)]),_:1}),s(a,{label:"操作",width:"180"},{default:n(i=>[e("div",lr,[e("button",{onClick:cr=>S(i.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,nr),s(re,{onCommand:U,trigger:"click"},{dropdown:n(()=>[s(Y,null,{default:n(()=>[s(L,{command:{action:"batchListing",row:i.row}},{default:n(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[19]},1032,["command"]),s(L,{command:{action:"smartCrop",row:i.row}},{default:n(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[20]},1032,["command"]),s(L,{command:{action:"oneClickCutout",row:i.row}},{default:n(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m-9 0h10m-10 0a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[21]},1032,["command"]),s(L,{command:{action:"superSplit",row:i.row}},{default:n(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[22]},1032,["command"]),s(L,{command:{action:"copyrightDetection",row:i.row}},{default:n(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})]),e("span",null,"侵权检测")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:n(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[C(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[ae,B.value]])]),e("div",dr,[e("div",ir," 共 "+d(l.value.total)+" 条记录 ",1),s(oe,{"current-page":l.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=i=>l.value.currentPage=i),"page-size":l.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=i=>l.value.pageSize=i),"page-sizes":[10,20,50,100],total:l.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:R,onCurrentChange:G,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),s(Oe,{modelValue:z.value,"onUpdate:modelValue":t[3]||(t[3]=i=>z.value=i),onSuccess:F},null,8,["modelValue"]),s(Tt,{modelValue:$.value,"onUpdate:modelValue":t[4]||(t[4]=i=>$.value=i),task:_.value},null,8,["modelValue","task"])],64)}}}),pr=J(ur,[["__scopeId","data-v-76e7460c"]]);export{pr as default};
