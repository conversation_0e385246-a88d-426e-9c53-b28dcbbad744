import{K as xe,d as re,r as h,L as X,e as Z,c as y,F as ee,g as t,h as k,j as o,a as e,M as P,C as m,k as ae,t as u,E as D,o as x,_ as oe,q,A as J,b as de,Q as ne,f as ve,p as be,G as fe}from"./index-gGKuRSZs.js";import{_ as _e}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-I6oFGs3s.js";import{r as le}from"./ArrowDownTrayIcon-DUfu9sNO.js";var A=(b=>(b.NO_RISK="no_risk",b.LOW_RISK="low_risk",b.MEDIUM_RISK="medium_risk",b.HIGH_RISK="high_risk",b))(A||{}),ie=(b=>(b.PENDING="pending",b.PROCESSING="processing",b.COMPLETED="completed",b.FAILED="failed",b))(ie||{});const te=xe({tasks:[{id:"CD001",imageCount:25,completedCount:25,status:"completed",operator:"admin",createTime:"2024-01-15 14:30:00",riskSummary:{noRisk:15,lowRisk:6,mediumRisk:3,highRisk:1}},{id:"CD002",imageCount:18,completedCount:12,status:"processing",operator:"admin",createTime:"2024-01-15 16:20:00"},{id:"CD003",imageCount:30,completedCount:30,status:"completed",operator:"admin",createTime:"2024-01-14 10:15:00",riskSummary:{noRisk:20,lowRisk:8,mediumRisk:2,highRisk:0}},{id:"CD004",imageCount:8,completedCount:0,status:"pending",operator:"admin",createTime:"2024-01-15 18:45:00"},{id:"CD005",imageCount:15,completedCount:0,status:"failed",operator:"admin",createTime:"2024-01-13 09:30:00"}],results:[{id:"DR001",taskId:"CD001",fileName:"product_image_001.jpg",originalImage:"https://picsum.photos/400/400?random=1",riskLevel:"high_risk",confidence:95,similarImages:[{id:"SI001",url:"https://picsum.photos/400/400?random=101",source:"shutterstock.com",similarity:95,copyright:"Shutterstock Inc."},{id:"SI002",url:"https://picsum.photos/400/400?random=102",source:"getty.com",similarity:88,copyright:"Getty Images"}],detectionTime:"2024-01-15 14:32:15"},{id:"DR002",taskId:"CD001",fileName:"product_image_002.jpg",originalImage:"https://picsum.photos/400/400?random=2",riskLevel:"medium_risk",confidence:72,similarImages:[{id:"SI003",url:"https://picsum.photos/400/400?random=103",source:"unsplash.com",similarity:72,copyright:"Unsplash License"}],detectionTime:"2024-01-15 14:32:28"},{id:"DR003",taskId:"CD001",fileName:"product_image_003.jpg",originalImage:"https://picsum.photos/400/400?random=3",riskLevel:"low_risk",confidence:45,similarImages:[{id:"SI004",url:"https://picsum.photos/400/400?random=104",source:"pexels.com",similarity:45,copyright:"Pexels License"}],detectionTime:"2024-01-15 14:32:41"},{id:"DR004",taskId:"CD001",fileName:"product_image_004.jpg",originalImage:"https://picsum.photos/400/400?random=4",riskLevel:"no_risk",confidence:15,similarImages:[],detectionTime:"2024-01-15 14:32:55"}]}),ue=b=>({no_risk:"无风险",low_risk:"低风险",medium_risk:"中风险",high_risk:"高风险"})[b],ye=b=>({pending:"等待中",processing:"检测中",completed:"已完成",failed:"失败"})[b],he=b=>te.results.filter(S=>S.taskId===b),we=b=>new Promise(S=>{setTimeout(()=>{const V={id:`CD${String(te.tasks.length+1).padStart(3,"0")}`,imageCount:b,completedCount:0,status:"pending",operator:"admin",createTime:new Date().toLocaleString("zh-CN")};te.tasks.unshift(V),S(V)},1e3)}),Ce={class:"space-y-6"},$e={class:"flex justify-start space-x-3"},Re={key:0,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},Se={class:"grid grid-cols-6 gap-4"},Ve={class:"relative"},Ie=["src","alt"],Te=["onClick"],De=["title"],je={key:0,class:"mt-4 flex justify-center"},Le={key:1,class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-12 text-center"},ze={key:2,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Me={class:"grid grid-cols-2 gap-4"},Be={class:"flex justify-between items-center"},Ue={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Pe={class:"flex space-x-3"},Ne=re({__name:"CreateDetectionDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(b,{emit:S}){const V=b,M=S,w=h(!1),I=h(!1),j=h([]),p=h([]),C=h(1),f=h(18),T=h("standard"),c=h("80");X(()=>V.modelValue,v=>{w.value=v}),X(w,v=>{M("update:modelValue",v)});const d=Z(()=>{const v=(C.value-1)*f.value,l=v+f.value;return p.value.slice(v,l)}),F=v=>{const l=new FileReader;l.onload=_=>{var H;const L=Date.now()+Math.random();p.value.push({id:L,name:v.name,url:(H=_.target)==null?void 0:H.result,file:v.raw})},l.readAsDataURL(v.raw)},N=v=>{const l=p.value.findIndex(_=>_.name===v.name);l>-1&&p.value.splice(l,1)},W=v=>{const l=p.value.findIndex(_=>_.id===v);if(l>-1){p.value.splice(l,1);const _=Math.ceil(p.value.length/f.value);C.value>_&&_>0&&(C.value=_)}},B=v=>{v.forEach(l=>{p.value.find(_=>_.id===l.id)||p.value.push({id:l.id,name:l.name,url:l.url||l.thumbnail})}),I.value=!1},E=v=>{C.value=v},i=async()=>{if(p.value.length===0){D.warning("请先选择图片");return}try{await we(p.value.length),D.success(`正在创建侵权检测任务，共 ${p.value.length} 张图片`),r(),M("success"),M("update:modelValue",!1)}catch{D.error("创建任务失败，请重试")}},r=()=>{p.value=[],j.value=[],C.value=1,T.value="standard",c.value="80"};return(v,l)=>{const _=k("el-button"),L=k("el-upload"),H=k("el-pagination"),U=k("el-option"),Q=k("el-select"),Y=k("el-dialog");return x(),y(ee,null,[t(Y,{modelValue:w.value,"onUpdate:modelValue":l[5]||(l[5]=a=>w.value=a),title:"新建侵权检测任务",width:"900px","align-center":"",onClose:r},{footer:o(()=>[e("div",Be,[e("div",Ue,u(p.value.length>0?`将检测 ${p.value.length} 张图片的侵权风险`:"请先选择图片"),1),e("div",Pe,[t(_,{onClick:l[4]||(l[4]=a=>w.value=!1)},{default:o(()=>l[14]||(l[14]=[m("取消")])),_:1,__:[14]}),t(_,{type:"primary",onClick:i,disabled:p.value.length===0},{default:o(()=>l[15]||(l[15]=[m(" 开始检测 ")])),_:1,__:[15]},8,["disabled"])])])]),default:o(()=>[e("div",Ce,[e("div",$e,[t(L,{ref:"uploadRef","file-list":j.value,"on-change":F,"on-remove":N,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:o(()=>[t(_,{type:"primary",size:"large"},{default:o(()=>l[7]||(l[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),m(" 上传图片 ")])),_:1,__:[7]})]),_:1},8,["file-list"]),t(_,{size:"large",onClick:l[0]||(l[0]=a=>I.value=!0)},{default:o(()=>l[8]||(l[8]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),m(" 从图库选择 ")])),_:1,__:[8]})]),p.value.length>0?(x(),y("div",Re,[e("div",Se,[(x(!0),y(ee,null,ae(d.value,a=>(x(),y("div",{key:a.id,class:"relative group"},[e("div",Ve,[e("img",{src:a.url,alt:a.name,class:"w-full h-24 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Ie),e("button",{onClick:s=>W(a.id),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},l[9]||(l[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Te)]),e("p",{class:"mt-2 text-xs text-gray-600 dark:text-dark-text-secondary truncate",title:a.name},u(a.name),9,De)]))),128))]),p.value.length>f.value?(x(),y("div",je,[t(H,{"current-page":C.value,"onUpdate:currentPage":l[1]||(l[1]=a=>C.value=a),"page-size":f.value,total:p.value.length,layout:"prev, pager, next",small:"",onCurrentChange:E},null,8,["current-page","page-size","total"])])):P("",!0)])):(x(),y("div",Le,l[10]||(l[10]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"暂无图片",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库中选择图片进行侵权检测",-1)]))),p.value.length>0?(x(),y("div",ze,[l[13]||(l[13]=e("h3",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"检测设置",-1)),e("div",Me,[e("div",null,[l[11]||(l[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 检测精度 ",-1)),t(Q,{modelValue:T.value,"onUpdate:modelValue":l[2]||(l[2]=a=>T.value=a),style:{width:"100%"}},{default:o(()=>[t(U,{label:"标准检测",value:"standard"}),t(U,{label:"高精度检测",value:"high"}),t(U,{label:"快速检测",value:"fast"})]),_:1},8,["modelValue"])]),e("div",null,[l[12]||(l[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 相似度阈值 ",-1)),t(Q,{modelValue:c.value,"onUpdate:modelValue":l[3]||(l[3]=a=>c.value=a),style:{width:"100%"}},{default:o(()=>[t(U,{label:"70% (宽松)",value:"70"}),t(U,{label:"80% (标准)",value:"80"}),t(U,{label:"90% (严格)",value:"90"})]),_:1},8,["modelValue"])])])])):P("",!0)])]),_:1},8,["modelValue"]),t(_e,{modelValue:I.value,"onUpdate:modelValue":l[6]||(l[6]=a=>I.value=a),"theme-color":"blue",onSelect:B},null,8,["modelValue"])],64)}}}),Ee=oe(Ne,[["__scopeId","data-v-b4659679"]]),Ke={key:0,class:"space-y-6"},Ge={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},Ae={class:"flex items-start space-x-4"},Fe={class:"flex-shrink-0"},He={class:"flex-1 space-y-2"},Oe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},We={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},qe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Qe={class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},Je={key:0,class:"space-y-4"},Xe={class:"flex items-start space-x-4"},Ye={class:"flex-shrink-0"},Ze={class:"flex-1"},et={class:"grid grid-cols-2 gap-4"},tt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},st={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},rt={key:0,class:"col-span-2"},ot={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},lt={class:"mt-3"},at={class:"flex items-center justify-between mb-1"},dt={class:"text-xs font-medium text-gray-900 dark:text-dark-text"},nt={class:"flex-shrink-0 flex flex-col space-y-2"},it={key:1,class:"text-center py-8"},ut={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"},ct={class:"text-sm text-blue-800 dark:text-blue-200"},mt={key:0},gt={key:1},kt={key:2},pt={key:3},xt={class:"flex justify-between items-center"},vt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},bt={class:"flex space-x-3"},ft=re({__name:"SimilarImagesDialog",props:{modelValue:{type:Boolean},detectionResult:{}},emits:["update:modelValue"],setup(b,{emit:S}){const V=b,M=S,w=h(!1);X(()=>V.modelValue,c=>{w.value=c}),X(w,c=>{M("update:modelValue",c)});const I=c=>({[A.NO_RISK]:"success",[A.LOW_RISK]:"warning",[A.MEDIUM_RISK]:"danger",[A.HIGH_RISK]:"danger"})[c],j=c=>c>=90?"#f56565":c>=80?"#ed8936":c>=70?"#ecc94b":"#48bb78",p=c=>{window.open(c,"_blank")},C=c=>{D.success(`正在举报来自 ${c.source} 的侵权图片...`)},f=()=>{D.success("正在导出相似图片报告...")},T=()=>{w.value=!1};return(c,d)=>{const F=k("el-image"),N=k("el-tag"),W=k("el-progress"),B=k("el-button"),E=k("el-dialog");return x(),q(E,{modelValue:w.value,"onUpdate:modelValue":d[0]||(d[0]=i=>w.value=i),title:"相似图片详情",width:"1000px","align-center":"",onClose:T},{footer:o(()=>{var i,r;return[e("div",xt,[e("div",vt,u(((r=(i=c.detectionResult)==null?void 0:i.similarImages)==null?void 0:r.length)||0)+" 张相似图片 ",1),e("div",bt,[t(B,{onClick:T},{default:o(()=>d[18]||(d[18]=[m("关闭")])),_:1,__:[18]}),t(B,{type:"primary",onClick:f},{default:o(()=>d[19]||(d[19]=[m("导出报告")])),_:1,__:[19]})])])]}),default:o(()=>{var i;return[c.detectionResult?(x(),y("div",Ke,[e("div",Ge,[d[5]||(d[5]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"原图信息",-1)),e("div",Ae,[e("div",Fe,[t(F,{src:c.detectionResult.originalImage,"preview-src-list":[c.detectionResult.originalImage],fit:"cover",class:"w-32 h-32 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])]),e("div",He,[e("div",null,[d[1]||(d[1]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"文件名：",-1)),e("span",Oe,u(c.detectionResult.fileName),1)]),e("div",null,[d[2]||(d[2]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"风险等级：",-1)),t(N,{type:I(c.detectionResult.riskLevel),size:"small"},{default:o(()=>[m(u(J(ue)(c.detectionResult.riskLevel)),1)]),_:1},8,["type"])]),e("div",null,[d[3]||(d[3]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"置信度：",-1)),e("span",We,u(c.detectionResult.confidence)+"%",1)]),e("div",null,[d[4]||(d[4]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"检测时间：",-1)),e("span",qe,u(c.detectionResult.detectionTime),1)])])])]),e("div",null,[e("h3",Qe," 相似图片 ("+u(((i=c.detectionResult.similarImages)==null?void 0:i.length)||0)+" 张) ",1),c.detectionResult.similarImages&&c.detectionResult.similarImages.length>0?(x(),y("div",Je,[(x(!0),y(ee,null,ae(c.detectionResult.similarImages,r=>(x(),y("div",{key:r.id,class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border p-4"},[e("div",Xe,[e("div",Ye,[t(F,{src:r.url,"preview-src-list":[r.url],fit:"cover",class:"w-24 h-24 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])]),e("div",Ze,[e("div",et,[e("div",null,[d[6]||(d[6]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"来源：",-1)),e("span",tt,u(r.source),1)]),e("div",null,[d[7]||(d[7]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"相似度：",-1)),e("span",st,u(r.similarity)+"%",1)]),r.copyright?(x(),y("div",rt,[d[8]||(d[8]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"版权信息：",-1)),e("span",ot,u(r.copyright),1)])):P("",!0)]),e("div",lt,[e("div",at,[d[9]||(d[9]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"相似度",-1)),e("span",dt,u(r.similarity)+"%",1)]),t(W,{percentage:r.similarity,"stroke-width":6,"show-text":!1,color:j(r.similarity)},null,8,["percentage","color"])])]),e("div",nt,[t(B,{size:"small",onClick:v=>p(r.url)},{default:o(()=>d[10]||(d[10]=[m(" 查看原图 ")])),_:2,__:[10]},1032,["onClick"]),t(B,{size:"small",type:"primary",onClick:v=>C(r)},{default:o(()=>d[11]||(d[11]=[m(" 举报侵权 ")])),_:2,__:[11]},1032,["onClick"])])])]))),128))])):(x(),y("div",it,d[12]||(d[12]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"未发现相似图片",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-dark-text-secondary"},"该图片未检测到潜在的侵权风险",-1)])))]),e("div",ut,[d[17]||(d[17]=e("h3",{class:"text-lg font-semibold text-blue-900 dark:text-blue-300 mb-2"},"风险评估建议",-1)),e("div",ct,[c.detectionResult.riskLevel==="high_risk"?(x(),y("p",mt,d[13]||(d[13]=[e("strong",null,"高风险：",-1),m("检测到高度相似的图片，建议立即停止使用该图片，或联系版权方获得授权。 ")]))):c.detectionResult.riskLevel==="medium_risk"?(x(),y("p",gt,d[14]||(d[14]=[e("strong",null,"中风险：",-1),m("检测到中度相似的图片，建议谨慎使用，可考虑进行图片修改或寻找替代图片。 ")]))):c.detectionResult.riskLevel==="low_risk"?(x(),y("p",kt,d[15]||(d[15]=[e("strong",null,"低风险：",-1),m("检测到轻微相似的图片，风险较低，但建议保持关注。 ")]))):(x(),y("p",pt,d[16]||(d[16]=[e("strong",null,"无风险：",-1),m("未检测到明显的侵权风险，可以安全使用。 ")])))])])])):P("",!0)]}),_:1},8,["modelValue"])}}}),_t=oe(ft,[["__scopeId","data-v-adf4fa82"]]),yt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},ht={class:"flex items-center space-x-3"},wt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Ct={class:"px-6 py-4 bg-gray-50 dark:bg-dark-card border-b border-gray-100 dark:border-dark-border"},$t={class:"grid grid-cols-2 md:grid-cols-4 gap-6"},Rt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},St={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Vt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},It={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Tt={key:0,class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Dt={class:"grid grid-cols-4 gap-4"},jt={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center"},Lt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},zt={class:"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center"},Mt={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},Bt={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center"},Ut={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Pt={class:"bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center"},Nt={class:"text-2xl font-bold text-red-600 dark:text-red-400"},Et={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Kt={class:"flex items-center justify-between"},Gt={class:"flex items-center space-x-4"},At={class:"flex space-x-2"},Ft={class:"px-6 pb-6"},Ht={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Ot={class:"flex justify-center"},Wt={class:"flex flex-col items-center"},qt={class:"text-sm font-medium"},Qt={class:"text-sm"},Jt={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card"},Xt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Yt={class:"flex space-x-3"},Zt=re({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(b,{emit:S}){const V=b,M=S,w=h(!1),I=h(!1),j=h(!1),p=h(null),C=h([]);X(()=>V.modelValue,i=>{w.value=i,i&&V.task&&c()}),X(w,i=>{M("update:modelValue",i)});const f=Z(()=>V.task?he(V.task.id):[]),T=Z(()=>{let i=f.value;return C.value.length>0&&(i=i.filter(r=>C.value.includes(r.riskLevel))),i}),c=()=>{I.value=!0,setTimeout(()=>{I.value=!1},500)},d=i=>({[A.NO_RISK]:"success",[A.LOW_RISK]:"warning",[A.MEDIUM_RISK]:"danger",[A.HIGH_RISK]:"danger"})[i],F=i=>i>=80?"#f56565":i>=60?"#ed8936":i>=40?"#ecc94b":"#48bb78",N=()=>{},W=i=>{p.value=i,j.value=!0},B=()=>{D.success("正在导出检测结果...")},E=()=>{w.value=!1,C.value=[]};return(i,r)=>{const v=k("el-option"),l=k("el-select"),_=k("el-button"),L=k("el-table-column"),H=k("el-image"),U=k("el-tag"),Q=k("el-progress"),Y=k("el-table"),a=k("el-dialog"),s=ne("loading");return x(),y(ee,null,[t(a,{modelValue:w.value,"onUpdate:modelValue":r[1]||(r[1]=g=>w.value=g),width:"1200px","before-close":E,"show-close":!1,class:"modern-dialog"},{header:o(()=>{var g;return[e("div",yt,[e("div",ht,[r[4]||(r[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])],-1)),e("div",null,[r[3]||(r[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"侵权检测详情",-1)),e("p",wt,"任务ID: "+u((g=i.task)==null?void 0:g.id),1)])]),e("button",{onClick:E,class:"p-2 hover:bg-gray-100 dark:hover:bg-dark-hover rounded-lg transition-colors duration-200"},r[5]||(r[5]=[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:o(()=>[e("div",Jt,[e("div",Xt," 共 "+u(T.value.length)+" 条检测结果 ",1),e("div",Yt,[t(_,{onClick:E},{default:o(()=>r[18]||(r[18]=[m("关闭")])),_:1,__:[18]}),t(_,{type:"primary",onClick:B},{default:o(()=>r[19]||(r[19]=[m("导出详情")])),_:1,__:[19]})])])]),default:o(()=>{var g,R,K,z,se,O;return[e("div",Ct,[e("div",$t,[e("div",null,[r[6]||(r[6]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"图片总数",-1)),e("p",Rt,u((g=i.task)==null?void 0:g.imageCount),1)]),e("div",null,[r[7]||(r[7]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"检测进度",-1)),e("p",St,u((R=i.task)==null?void 0:R.completedCount)+"/"+u((K=i.task)==null?void 0:K.imageCount),1)]),e("div",null,[r[8]||(r[8]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"操作员",-1)),e("p",Vt,u((z=i.task)==null?void 0:z.operator),1)]),e("div",null,[r[9]||(r[9]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",It,u((se=i.task)==null?void 0:se.createTime),1)])])]),(O=i.task)!=null&&O.riskSummary?(x(),y("div",Tt,[r[14]||(r[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"风险分布",-1)),e("div",Dt,[e("div",jt,[e("div",Lt,u(i.task.riskSummary.noRisk),1),r[10]||(r[10]=e("div",{class:"text-sm text-green-600 dark:text-green-400"},"无风险",-1))]),e("div",zt,[e("div",Mt,u(i.task.riskSummary.lowRisk),1),r[11]||(r[11]=e("div",{class:"text-sm text-yellow-600 dark:text-yellow-400"},"低风险",-1))]),e("div",Bt,[e("div",Ut,u(i.task.riskSummary.mediumRisk),1),r[12]||(r[12]=e("div",{class:"text-sm text-orange-600 dark:text-orange-400"},"中风险",-1))]),e("div",Pt,[e("div",Nt,u(i.task.riskSummary.highRisk),1),r[13]||(r[13]=e("div",{class:"text-sm text-red-600 dark:text-red-400"},"高风险",-1))])])])):P("",!0),e("div",Et,[e("div",Kt,[e("div",Gt,[r[15]||(r[15]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"检测结果",-1)),t(l,{modelValue:C.value,"onUpdate:modelValue":r[0]||(r[0]=$=>C.value=$),placeholder:"筛选风险等级",multiple:"",clearable:"",style:{width:"200px"},onChange:N},{default:o(()=>[t(v,{label:"无风险",value:"no_risk"}),t(v,{label:"低风险",value:"low_risk"}),t(v,{label:"中风险",value:"medium_risk"}),t(v,{label:"高风险",value:"high_risk"})]),_:1},8,["modelValue"])]),e("div",At,[t(_,{onClick:B,size:"small"},{default:o(()=>r[16]||(r[16]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),m(" 导出结果 ")])),_:1,__:[16]})])])]),e("div",Ft,[e("div",Ht,[de((x(),q(Y,{data:T.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:o(()=>[t(L,{prop:"fileName",label:"文件名",width:"200"}),t(L,{label:"原图",width:"100",align:"center"},{default:o($=>[e("div",Ot,[t(H,{src:$.row.originalImage,"preview-src-list":[$.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])])]),_:1}),t(L,{label:"风险等级",width:"120",align:"center"},{default:o($=>[t(U,{type:d($.row.riskLevel),size:"small"},{default:o(()=>[m(u(J(ue)($.row.riskLevel)),1)]),_:2},1032,["type"])]),_:1}),t(L,{label:"置信度",width:"100",align:"center"},{default:o($=>[e("div",Wt,[e("span",qt,u($.row.confidence)+"%",1),t(Q,{percentage:$.row.confidence,"stroke-width":4,"show-text":!1,color:F($.row.confidence),class:"w-full mt-1"},null,8,["percentage","color"])])]),_:1}),t(L,{label:"相似图片",width:"120",align:"center"},{default:o($=>{var G;return[e("span",Qt,u(((G=$.row.similarImages)==null?void 0:G.length)||0)+" 张",1)]}),_:1}),t(L,{prop:"detectionTime",label:"检测时间",width:"160",align:"center"}),t(L,{label:"操作",width:"120",align:"center",fixed:"right"},{default:o($=>[t(_,{type:"primary",size:"small",onClick:G=>W($.row),disabled:!$.row.similarImages||$.row.similarImages.length===0},{default:o(()=>r[17]||(r[17]=[m(" 查看详情 ")])),_:2,__:[17]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[s,I.value]])])])]}),_:1},8,["modelValue"]),t(_t,{modelValue:j.value,"onUpdate:modelValue":r[2]||(r[2]=g=>j.value=g),"detection-result":p.value},null,8,["modelValue","detection-result"])],64)}}}),es=oe(Zt,[["__scopeId","data-v-bcdd4e2a"]]),ts={class:"space-y-6"},ss={class:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"},rs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},os={class:"flex items-center"},ls={class:"ml-4"},as={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ds={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},ns={class:"flex items-center"},is={class:"ml-4"},us={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},cs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},ms={class:"flex items-center"},gs={class:"ml-4"},ks={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},ps={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},xs={class:"flex items-center"},vs={class:"ml-4"},bs={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},fs={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},_s={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},ys={class:"flex items-center space-x-3"},hs={class:"flex items-center space-x-3"},ws={key:0,class:"mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"},Cs={class:"flex items-center justify-between"},$s={class:"text-sm text-blue-700 dark:text-blue-300"},Rs={class:"flex space-x-2"},Ss={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},Vs={class:"font-medium"},Is={class:"flex flex-col items-center space-y-1"},Ts={class:"text-sm font-medium"},Ds={key:0,class:"flex justify-center space-x-2"},js={key:1,class:"text-gray-400 dark:text-dark-text-secondary"},Ls={class:"flex justify-center space-x-2"},zs={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Ms={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Bs=re({__name:"index",setup(b){const S=h(!1),V=h(!1),M=h(!1),w=h(null),I=h([]),j=h(""),p=h([]),C=h(""),f=h({currentPage:1,pageSize:10,total:0}),T=Z(()=>te.tasks.filter(s=>s.status===ie.COMPLETED).reduce((s,g)=>(g.riskSummary&&(s.noRisk+=g.riskSummary.noRisk,s.lowRisk+=g.riskSummary.lowRisk,s.mediumRisk+=g.riskSummary.mediumRisk,s.highRisk+=g.riskSummary.highRisk),s),{noRisk:0,lowRisk:0,mediumRisk:0,highRisk:0})),c=Z(()=>{let a=te.tasks;if(j.value&&(a=a.filter(R=>R.status===j.value)),p.value.length>0&&(a=a.filter(R=>R.riskSummary?p.value.some(K=>{switch(K){case"no_risk":return R.riskSummary.noRisk>0;case"low_risk":return R.riskSummary.lowRisk>0;case"medium_risk":return R.riskSummary.mediumRisk>0;case"high_risk":return R.riskSummary.highRisk>0;default:return!1}}):!1)),C.value){const R=C.value.toLowerCase();a=a.filter(K=>K.id.toLowerCase().includes(R)||K.operator.toLowerCase().includes(R))}f.value.total=a.length;const s=(f.value.currentPage-1)*f.value.pageSize,g=s+f.value.pageSize;return a.slice(s,g)});ve(()=>{d()});const d=()=>{S.value=!0,setTimeout(()=>{S.value=!1},500)},F=a=>({pending:"info",processing:"warning",completed:"success",failed:"danger"})[a]||"info",N=()=>{f.value.currentPage=1},W=a=>{I.value=a},B=a=>{w.value=a,M.value=!0},E=a=>{const{action:s,row:g}=a;switch(s){case"titleGenerate":l(g);break;case"batchListing":_(g);break;case"smartCrop":L(g);break;case"oneClickCutout":H(g);break;case"superSplit":U(g);break}},i=()=>{D.success("导出表格功能开发中...")},r=()=>{D.success(`正在批量导出 ${I.value.length} 个任务...`)},v=()=>{D.success("侵权检测任务创建成功！"),d()},l=a=>{D.success(`正在为检测任务 ${a.id} 创建标题生成任务...`)},_=a=>{D.success(`正在为检测任务 ${a.id} 创建批量刊登任务...`)},L=a=>{D.success(`正在为检测任务 ${a.id} 创建智能裁图任务...`)},H=a=>{D.success(`正在为检测任务 ${a.id} 创建一键抠图任务...`)},U=a=>{D.success(`正在为检测任务 ${a.id} 创建超级裂变任务...`)},Q=a=>{f.value.pageSize=a,f.value.currentPage=1,d()},Y=a=>{f.value.currentPage=a,d()};return(a,s)=>{const g=k("el-option"),R=k("el-select"),K=k("el-input"),z=k("el-table-column"),se=k("el-progress"),O=k("el-tag"),$=k("el-button"),G=k("el-dropdown-item"),ce=k("el-dropdown-menu"),me=k("el-dropdown"),ge=k("el-table"),ke=k("el-pagination"),pe=ne("loading");return x(),y(ee,null,[e("div",ts,[s[27]||(s[27]=be('<div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800" data-v-65897bdf><div class="flex items-center space-x-3" data-v-65897bdf><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center" data-v-65897bdf><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-65897bdf><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" data-v-65897bdf></path></svg></div><div data-v-65897bdf><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-65897bdf>侵权检测</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-65897bdf>AI智能图片侵权检测和风险评估工具</p></div></div></div>',1)),e("div",ss,[e("div",rs,[e("div",os,[s[9]||(s[9]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])])],-1)),e("div",ls,[s[8]||(s[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"无风险",-1)),e("p",as,u(T.value.noRisk),1)])])]),e("div",ds,[e("div",ns,[s[11]||(s[11]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])])],-1)),e("div",is,[s[10]||(s[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"低风险",-1)),e("p",us,u(T.value.lowRisk),1)])])]),e("div",cs,[e("div",ms,[s[13]||(s[13]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])])],-1)),e("div",gs,[s[12]||(s[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"中风险",-1)),e("p",ks,u(T.value.mediumRisk),1)])])]),e("div",ps,[e("div",xs,[s[15]||(s[15]=e("div",{class:"flex-shrink-0"},[e("div",{class:"w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-5 h-5 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})])])],-1)),e("div",vs,[s[14]||(s[14]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"高风险",-1)),e("p",bs,u(T.value.highRisk),1)])])])]),e("div",fs,[e("div",_s,[e("div",ys,[e("button",{onClick:s[0]||(s[0]=n=>V.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[t(J(fe),{class:"w-5 h-5 mr-2"}),s[16]||(s[16]=m(" 新建检测 "))]),e("button",{onClick:i,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card border border-gray-300 dark:border-dark-border text-gray-700 dark:text-dark-text hover:bg-gray-50 dark:hover:bg-dark-hover font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200"},[t(J(le),{class:"w-5 h-5 mr-2"}),s[17]||(s[17]=m(" 导出表格 "))])]),e("div",hs,[t(R,{modelValue:j.value,"onUpdate:modelValue":s[1]||(s[1]=n=>j.value=n),placeholder:"状态筛选",clearable:"",style:{width:"120px"},onChange:N},{default:o(()=>[t(g,{label:"等待中",value:"pending"}),t(g,{label:"检测中",value:"processing"}),t(g,{label:"已完成",value:"completed"}),t(g,{label:"失败",value:"failed"})]),_:1},8,["modelValue"]),t(R,{modelValue:p.value,"onUpdate:modelValue":s[2]||(s[2]=n=>p.value=n),placeholder:"风险等级",multiple:"",clearable:"",style:{width:"140px"},onChange:N},{default:o(()=>[t(g,{label:"无风险",value:"no_risk"}),t(g,{label:"低风险",value:"low_risk"}),t(g,{label:"中风险",value:"medium_risk"}),t(g,{label:"高风险",value:"high_risk"})]),_:1},8,["modelValue"]),t(K,{modelValue:C.value,"onUpdate:modelValue":s[3]||(s[3]=n=>C.value=n),placeholder:"搜索任务ID...",style:{width:"200px"},onInput:N,clearable:""},{prefix:o(()=>s[18]||(s[18]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"])])]),I.value.length>0?(x(),y("div",ws,[e("div",Cs,[e("span",$s," 已选择 "+u(I.value.length)+" 个任务 ",1),e("div",Rs,[e("button",{onClick:r,class:"inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200"},[t(J(le),{class:"w-4 h-4 mr-1"}),s[19]||(s[19]=m(" 批量导出 "))])])])])):P("",!0)]),e("div",Ss,[de((x(),q(ge,{data:c.value,onSelectionChange:W,style:{width:"100%"},class:"modern-table"},{default:o(()=>[t(z,{type:"selection",width:"55",align:"center"}),t(z,{prop:"id",label:"任务ID",width:"100",align:"center"}),t(z,{label:"图片数量",width:"120",align:"center"},{default:o(n=>[e("span",Vs,u(n.row.imageCount),1)]),_:1}),t(z,{label:"检测进度",width:"150",align:"center"},{default:o(n=>[e("div",Is,[e("span",Ts,u(n.row.completedCount)+"/"+u(n.row.imageCount),1),t(se,{percentage:Math.round(n.row.completedCount/n.row.imageCount*100),"stroke-width":6,"show-text":!1,class:"w-full"},null,8,["percentage"])])]),_:1}),t(z,{label:"风险分布",width:"200",align:"center"},{default:o(n=>[n.row.riskSummary?(x(),y("div",Ds,[n.row.riskSummary.noRisk>0?(x(),q(O,{key:0,type:"success",size:"small"},{default:o(()=>[m(" 无风险: "+u(n.row.riskSummary.noRisk),1)]),_:2},1024)):P("",!0),n.row.riskSummary.lowRisk>0?(x(),q(O,{key:1,type:"warning",size:"small"},{default:o(()=>[m(" 低风险: "+u(n.row.riskSummary.lowRisk),1)]),_:2},1024)):P("",!0),n.row.riskSummary.mediumRisk>0?(x(),q(O,{key:2,type:"danger",size:"small"},{default:o(()=>[m(" 中风险: "+u(n.row.riskSummary.mediumRisk),1)]),_:2},1024)):P("",!0),n.row.riskSummary.highRisk>0?(x(),q(O,{key:3,type:"danger",size:"small"},{default:o(()=>[m(" 高风险: "+u(n.row.riskSummary.highRisk),1)]),_:2},1024)):P("",!0)])):(x(),y("span",js,"-"))]),_:1}),t(z,{label:"状态",width:"100",align:"center"},{default:o(n=>[t(O,{type:F(n.row.status),size:"small"},{default:o(()=>[m(u(J(ye)(n.row.status)),1)]),_:2},1032,["type"])]),_:1}),t(z,{prop:"operator",label:"操作员",width:"100",align:"center"}),t(z,{prop:"createTime",label:"创建时间",width:"160",align:"center"}),t(z,{label:"操作",width:"200",align:"center",fixed:"right"},{default:o(n=>[e("div",Ls,[t($,{type:"primary",size:"small",onClick:Us=>B(n.row),disabled:n.row.status!=="completed"},{default:o(()=>s[20]||(s[20]=[m(" 查看详情 ")])),_:2,__:[20]},1032,["onClick","disabled"]),t(me,{onCommand:E,trigger:"click"},{dropdown:o(()=>[t(ce,null,{default:o(()=>[t(G,{command:{action:"titleGenerate",row:n.row}},{default:o(()=>s[22]||(s[22]=[m("标题生成")])),_:2,__:[22]},1032,["command"]),t(G,{command:{action:"batchListing",row:n.row}},{default:o(()=>s[23]||(s[23]=[m("批量刊登")])),_:2,__:[23]},1032,["command"]),t(G,{command:{action:"smartCrop",row:n.row}},{default:o(()=>s[24]||(s[24]=[m("智能裁图")])),_:2,__:[24]},1032,["command"]),t(G,{command:{action:"oneClickCutout",row:n.row}},{default:o(()=>s[25]||(s[25]=[m("一键抠图")])),_:2,__:[25]},1032,["command"]),t(G,{command:{action:"superSplit",row:n.row}},{default:o(()=>s[26]||(s[26]=[m("超级裂变")])),_:2,__:[26]},1032,["command"])]),_:2},1024)]),default:o(()=>[t($,{type:"info",size:"small"},{default:o(()=>s[21]||(s[21]=[m(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})],-1)])),_:1,__:[21]})]),_:2},1024)])]),_:1})]),_:1},8,["data"])),[[pe,S.value]]),e("div",zs,[e("div",Ms," 共 "+u(f.value.total)+" 条记录 ",1),t(ke,{"current-page":f.value.currentPage,"onUpdate:currentPage":s[4]||(s[4]=n=>f.value.currentPage=n),"page-size":f.value.pageSize,"onUpdate:pageSize":s[5]||(s[5]=n=>f.value.pageSize=n),"page-sizes":[10,20,50,100],total:f.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:Q,onCurrentChange:Y,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),t(Ee,{modelValue:V.value,"onUpdate:modelValue":s[6]||(s[6]=n=>V.value=n),onSuccess:v},null,8,["modelValue"]),t(es,{modelValue:M.value,"onUpdate:modelValue":s[7]||(s[7]=n=>M.value=n),task:w.value},null,8,["modelValue","task"])],64)}}}),Ks=oe(Bs,[["__scopeId","data-v-65897bdf"]]);export{Ks as default};
