import{d as ve,r as p,K as F,e as be,f as fe,c as j,a,g as l,C as h,A as V,R as ke,t as i,j as o,h as g,W as we,b as ye,Q as _e,q as C,F as E,k as L,n as he,M as Ve,E as k,Y as N,o as f}from"./index-D_iwS02E.js";import{r as Ce}from"./UsersIcon-DeS6c7EW.js";import{r as Se}from"./CheckCircleIcon-B8lXk3M2.js";import{r as Ie}from"./ClockIcon-r3ALi7GK.js";import{r as Ue}from"./MagnifyingGlassIcon-CrHoZUMX.js";const Te={class:"p-8"},Pe={class:"flex items-center justify-between mb-8"},$e={class:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8"},ze={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},Be={class:"flex items-center"},Fe={class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/20 rounded-lg flex items-center justify-center"},je={class:"ml-4"},De={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},qe={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},Re={class:"flex items-center"},Ee={class:"w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-lg flex items-center justify-center"},Le={class:"ml-4"},Ne={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Me={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm"},Ke={class:"flex items-center"},Oe={class:"w-12 h-12 bg-amber-100 dark:bg-amber-900/20 rounded-lg flex items-center justify-center"},We={class:"ml-4"},Ae={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Qe={class:"bg-white dark:bg-dark-card rounded-lg p-6 shadow-sm mb-6"},Ye={class:"grid grid-cols-1 md:grid-cols-3 gap-4"},Ge={class:"bg-white dark:bg-dark-card rounded-lg shadow-sm overflow-hidden"},He={class:"overflow-x-auto"},Je=["src","alt"],Xe={class:"font-medium text-gray-900 dark:text-dark-text"},Ze={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},et={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},tt={class:"text-sm"},at={class:"text-gray-900 dark:text-dark-text"},lt={class:"text-gray-500 dark:text-dark-text-secondary"},rt={class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400"},ot={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},st={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},dt={class:"flex items-center space-x-2"},nt=["onClick"],it=["onClick"],ut=["onClick"],mt=["onClick"],ct={class:"px-6 py-4 border-t border-gray-200 dark:border-dark-border"},pt={class:"text-sm text-gray-500"},gt={class:"flex justify-end space-x-3"},xt={class:"text-sm text-gray-500"},vt={class:"flex justify-end space-x-3"},Vt=ve({__name:"index",setup(bt,{expose:Y}){const D=p(!1),S=p(!1),I=p(!1),w=p(!1),y=p(!1),U=p(null),G=p([]),T=p(),P=p(),d=F({domain:"",username:"",password:"",confirmPassword:"",nickname:"",phone:"",email:"",roleId:"",remark:""}),s=F({domain:"",username:"",nickname:"",phone:"",email:"",roleId:"",remark:""}),M=p(8),K=p(6),H=p(3),x=F({status:"",roleId:"",keyword:""}),c=F({currentPage:1,pageSize:10,total:0}),v=p([]),$=p([{id:"admin",name:"管理员",description:"拥有所有权限"},{id:"operator",name:"操作员",description:"基本操作权限"},{id:"viewer",name:"查看员",description:"只读权限"}]),J={domain:[{required:!0,message:"请输入域名",trigger:"blur"}],username:[{required:!0,message:"请输入账号",trigger:"blur"},{min:3,max:20,message:"账号长度在 3 到 20 个字符",trigger:"blur"}],password:[{required:!0,message:"请输入密码",trigger:"blur"},{min:6,max:20,message:"密码长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,message:"请确认密码",trigger:"blur"},{validator:(r,e,n)=>{e!==d.password?n(new Error("两次输入的密码不一致")):n()},trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度在 2 到 20 个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],roleId:[{required:!0,message:"请选择权限组",trigger:"change"}]},X={domain:[{required:!0,message:"请输入域名",trigger:"blur"}],nickname:[{required:!0,message:"请输入昵称",trigger:"blur"},{min:2,max:20,message:"昵称长度在 2 到 20 个字符",trigger:"blur"}],phone:[{pattern:/^1[3-9]\d{9}$/,message:"请输入正确的手机号",trigger:"blur"}],email:[{type:"email",message:"请输入正确的邮箱格式",trigger:"blur"}],roleId:[{required:!0,message:"请选择权限组",trigger:"change"}]},Z=be(()=>{const r=(c.currentPage-1)*c.pageSize,e=r+c.pageSize;return v.value.slice(r,e)}),ee=r=>{const e=$.value.find(n=>n.id===r);return(e==null?void 0:e.name)||r},q=()=>{c.currentPage=1,_()},te=()=>{x.status="",x.roleId="",x.keyword="",q()},ae=r=>{G.value=r},le=r=>{c.pageSize=r,_()},re=r=>{c.currentPage=r,_()},oe=r=>{U.value=r,s.domain=r.domain,s.username=r.username,s.nickname=r.nickname,s.phone=r.phone||"",s.email=r.email||"",s.roleId=r.roleId,s.remark=r.remark||"",y.value=!0},se=()=>{S.value||(O(),w.value=!1)},de=()=>{I.value||(W(),y.value=!1)},O=()=>{var r;(r=T.value)==null||r.resetFields(),Object.assign(d,{domain:"",username:"",password:"",confirmPassword:"",nickname:"",phone:"",email:"",roleId:"",remark:""})},W=()=>{var r;(r=P.value)==null||r.resetFields(),Object.assign(s,{domain:"",username:"",nickname:"",phone:"",email:"",roleId:"",remark:""})},ne=async()=>{if(T.value)try{await T.value.validate(),S.value=!0,await new Promise(e=>setTimeout(e,1e3));const r={id:`SUB${Date.now()}`,username:d.username,nickname:d.nickname,domain:d.domain,phone:d.phone||void 0,email:d.email||void 0,roleId:d.roleId,status:"active",createTime:new Date().toLocaleString(),remark:d.remark||void 0};v.value.unshift(r),c.total=v.value.length,M.value++,K.value++,O(),w.value=!1,k.success("子账号创建成功")}catch{k.error("创建失败，请检查输入信息")}finally{S.value=!1}},ie=async()=>{if(!(!P.value||!U.value))try{await P.value.validate(),I.value=!0,await new Promise(e=>setTimeout(e,1e3));const r=v.value.findIndex(e=>e.id===U.value.id);r>-1&&Object.assign(v.value[r],{domain:s.domain,nickname:s.nickname,phone:s.phone||void 0,email:s.email||void 0,roleId:s.roleId,remark:s.remark||void 0}),W(),y.value=!1,k.success("子账号信息更新成功")}catch{k.error("更新失败，请检查输入信息")}finally{I.value=!1}},ue=async r=>{const e=r.status==="active"?"禁用":"启用";try{await N.confirm(`确定要${e}子账号 "${r.nickname}" 吗？`,"确认操作",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(n=>setTimeout(n,500)),r.status=r.status==="active"?"disabled":"active",k.success(`${e}成功`)}catch{}},me=async r=>{try{await N.confirm(`确定要重置子账号 "${r.nickname}" 的密码吗？新密码将发送到其邮箱。`,"确认重置密码",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}),await new Promise(e=>setTimeout(e,500)),k.success("密码重置成功，新密码已发送到用户邮箱")}catch{}},ce=async r=>{try{await N.confirm(`确定要删除子账号 "${r.nickname}" 吗？此操作不可恢复。`,"确认删除",{confirmButtonText:"确定",cancelButtonText:"取消",type:"error"}),await new Promise(n=>setTimeout(n,500));const e=v.value.findIndex(n=>n.id===r.id);e>-1&&(v.value.splice(e,1),c.total=v.value.length),k.success("删除成功")}catch{}},_=()=>{D.value=!0,setTimeout(()=>{const r=[{id:"SUB001",username:"zhangsan",nickname:"张三",domain:"company.com",phone:"13800138001",email:"<EMAIL>",avatar:"/avatars/zhangsan.jpg",roleId:"admin",status:"active",lastLoginTime:"2024-01-15 14:30:00",createTime:"2024-01-01 10:00:00",remark:"技术负责人"},{id:"SUB002",username:"lisi",nickname:"李四",domain:"company.com",phone:"13800138002",email:"<EMAIL>",roleId:"operator",status:"active",lastLoginTime:"2024-01-15 09:15:00",createTime:"2024-01-02 11:00:00",remark:"运营专员"},{id:"SUB003",username:"wangwu",nickname:"王五",domain:"company.com",email:"<EMAIL>",roleId:"viewer",status:"disabled",lastLoginTime:"2024-01-10 16:45:00",createTime:"2024-01-03 14:00:00",remark:"临时账号"}];v.value=r,c.total=r.length,D.value=!1},500)};return Y({refreshData:()=>{_()}}),fe(()=>{_()}),(r,e)=>{const n=g("el-option"),z=g("el-select"),m=g("el-input"),b=g("el-table-column"),pe=g("el-table"),ge=g("el-pagination"),u=g("el-form-item"),A=g("el-form"),B=g("el-button"),Q=g("el-dialog"),xe=_e("loading");return f(),j("div",Te,[a("div",Pe,[e[27]||(e[27]=a("div",null,[a("h2",{class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},"子账号管理"),a("p",{class:"mt-2 text-sm text-gray-600 dark:text-dark-text-secondary"},"管理您的子账号，分配不同的权限")],-1)),a("button",{onClick:e[0]||(e[0]=t=>w.value=!0),class:"inline-flex items-center px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"},[l(V(ke),{class:"w-4 h-4 mr-2"}),e[26]||(e[26]=h(" 创建子账号 "))])]),a("div",$e,[a("div",ze,[a("div",Be,[a("div",Fe,[l(V(Ce),{class:"w-6 h-6 text-blue-600 dark:text-blue-400"})]),a("div",je,[e[28]||(e[28]=a("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"总子账号",-1)),a("p",De,i(M.value),1)])])]),a("div",qe,[a("div",Re,[a("div",Ee,[l(V(Se),{class:"w-6 h-6 text-green-600 dark:text-green-400"})]),a("div",Le,[e[29]||(e[29]=a("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"活跃账号",-1)),a("p",Ne,i(K.value),1)])])]),a("div",Me,[a("div",Ke,[a("div",Oe,[l(V(Ie),{class:"w-6 h-6 text-amber-600 dark:text-amber-400"})]),a("div",We,[e[30]||(e[30]=a("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"今日登录",-1)),a("p",Ae,i(H.value),1)])])])]),a("div",Qe,[a("div",Ye,[a("div",null,[e[31]||(e[31]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 账号状态 ",-1)),l(z,{modelValue:x.status,"onUpdate:modelValue":e[1]||(e[1]=t=>x.status=t),placeholder:"全部状态",clearable:"",class:"w-full"},{default:o(()=>[l(n,{label:"全部状态",value:""}),l(n,{label:"正常",value:"active"}),l(n,{label:"禁用",value:"disabled"})]),_:1},8,["modelValue"])]),a("div",null,[e[32]||(e[32]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 权限组 ",-1)),l(z,{modelValue:x.roleId,"onUpdate:modelValue":e[2]||(e[2]=t=>x.roleId=t),placeholder:"全部权限组",clearable:"",class:"w-full"},{default:o(()=>[l(n,{label:"全部权限组",value:""}),(f(!0),j(E,null,L($.value,t=>(f(),C(n,{key:t.id,label:t.name,value:t.id},null,8,["label","value"]))),128))]),_:1},8,["modelValue"])]),a("div",null,[e[33]||(e[33]=a("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 搜索 ",-1)),l(m,{modelValue:x.keyword,"onUpdate:modelValue":e[3]||(e[3]=t=>x.keyword=t),placeholder:"搜索账号或昵称",clearable:"",onKeyup:we(q,["enter"])},{suffix:o(()=>[l(V(Ue),{class:"w-4 h-4 text-gray-400"})]),_:1},8,["modelValue"])])]),a("div",{class:"flex justify-end mt-4 space-x-3"},[a("button",{onClick:te,class:"px-4 py-2 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-900 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-border hover:bg-gray-100 dark:hover:bg-dark-card rounded-lg transition-all duration-200"}," 重置 "),a("button",{onClick:q,class:"px-4 py-2 text-sm font-medium text-white bg-amber-500 hover:bg-amber-600 rounded-lg transition-colors"}," 搜索 ")])]),a("div",Ge,[a("div",He,[ye((f(),C(pe,{data:Z.value,style:{width:"100%"},onSelectionChange:ae,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:o(()=>[l(b,{type:"selection",width:"55"}),l(b,{label:"头像",width:"80"},{default:o(t=>[a("img",{src:t.row.avatar||"/default-avatar.png",alt:t.row.nickname,class:"w-10 h-10 rounded-full object-cover"},null,8,Je)]),_:1}),l(b,{label:"账号信息","min-width":"200"},{default:o(t=>[a("div",null,[a("div",Xe,i(t.row.nickname),1),a("div",Ze,i(t.row.username),1),a("div",et,i(t.row.domain),1)])]),_:1}),l(b,{label:"联系方式",width:"150"},{default:o(t=>[a("div",tt,[a("div",at,i(t.row.phone||"-"),1),a("div",lt,i(t.row.email||"-"),1)])]),_:1}),l(b,{label:"权限组",width:"120"},{default:o(t=>[a("span",rt,i(ee(t.row.roleId)),1)]),_:1}),l(b,{label:"状态",width:"100"},{default:o(t=>[a("span",{class:he(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",[t.row.status==="active"?"bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-400":"bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-400"]])},i(t.row.status==="active"?"正常":"禁用"),3)]),_:1}),l(b,{label:"最后登录",width:"160"},{default:o(t=>[a("span",ot,i(t.row.lastLoginTime||"从未登录"),1)]),_:1}),l(b,{label:"创建时间",width:"160"},{default:o(t=>[a("span",st,i(t.row.createTime),1)]),_:1}),l(b,{label:"操作",width:"200",fixed:"right"},{default:o(t=>[a("div",dt,[a("button",{onClick:R=>oe(t.row),class:"text-sm text-amber-600 dark:text-amber-400 hover:text-amber-700 dark:hover:text-amber-300"}," 编辑 ",8,nt),a("button",{onClick:R=>ue(t.row),class:"text-sm text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300"},i(t.row.status==="active"?"禁用":"启用"),9,it),a("button",{onClick:R=>me(t.row),class:"text-sm text-purple-600 dark:text-purple-400 hover:text-purple-700 dark:hover:text-purple-300"}," 重置密码 ",8,ut),a("button",{onClick:R=>ce(t.row),class:"text-sm text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300"}," 删除 ",8,mt)])]),_:1})]),_:1},8,["data"])),[[xe,D.value]])]),a("div",ct,[l(ge,{"current-page":c.currentPage,"onUpdate:currentPage":e[4]||(e[4]=t=>c.currentPage=t),"page-size":c.pageSize,"onUpdate:pageSize":e[5]||(e[5]=t=>c.pageSize=t),"page-sizes":[10,20,50,100],total:c.total,layout:"total, sizes, prev, pager, next, jumper",onSizeChange:le,onCurrentChange:re},null,8,["current-page","page-size","total"])])]),l(Q,{modelValue:w.value,"onUpdate:modelValue":e[16]||(e[16]=t=>w.value=t),title:"创建子账号",width:"600px","before-close":se},{footer:o(()=>[a("div",gt,[l(B,{onClick:e[15]||(e[15]=t=>w.value=!1)},{default:o(()=>e[34]||(e[34]=[h("取消")])),_:1,__:[34]}),l(B,{type:"primary",onClick:ne,loading:S.value},{default:o(()=>e[35]||(e[35]=[h(" 创建子账号 ")])),_:1,__:[35]},8,["loading"])])]),default:o(()=>[l(A,{ref_key:"createFormRef",ref:T,model:d,rules:J,"label-width":"100px",class:"space-y-4"},{default:o(()=>[l(u,{label:"域名",prop:"domain"},{default:o(()=>[l(m,{modelValue:d.domain,"onUpdate:modelValue":e[6]||(e[6]=t=>d.domain=t),placeholder:"请输入域名，如：company.com"},null,8,["modelValue"])]),_:1}),l(u,{label:"账号",prop:"username"},{default:o(()=>[l(m,{modelValue:d.username,"onUpdate:modelValue":e[7]||(e[7]=t=>d.username=t),placeholder:"请输入登录账号"},null,8,["modelValue"])]),_:1}),l(u,{label:"密码",prop:"password"},{default:o(()=>[l(m,{modelValue:d.password,"onUpdate:modelValue":e[8]||(e[8]=t=>d.password=t),type:"password",placeholder:"请输入密码","show-password":""},null,8,["modelValue"])]),_:1}),l(u,{label:"确认密码",prop:"confirmPassword"},{default:o(()=>[l(m,{modelValue:d.confirmPassword,"onUpdate:modelValue":e[9]||(e[9]=t=>d.confirmPassword=t),type:"password",placeholder:"请再次输入密码","show-password":""},null,8,["modelValue"])]),_:1}),l(u,{label:"昵称",prop:"nickname"},{default:o(()=>[l(m,{modelValue:d.nickname,"onUpdate:modelValue":e[10]||(e[10]=t=>d.nickname=t),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),l(u,{label:"手机号",prop:"phone"},{default:o(()=>[l(m,{modelValue:d.phone,"onUpdate:modelValue":e[11]||(e[11]=t=>d.phone=t),placeholder:"请输入手机号（可选）"},null,8,["modelValue"])]),_:1}),l(u,{label:"邮箱",prop:"email"},{default:o(()=>[l(m,{modelValue:d.email,"onUpdate:modelValue":e[12]||(e[12]=t=>d.email=t),placeholder:"请输入邮箱（可选）"},null,8,["modelValue"])]),_:1}),l(u,{label:"权限组",prop:"roleId"},{default:o(()=>[l(z,{modelValue:d.roleId,"onUpdate:modelValue":e[13]||(e[13]=t=>d.roleId=t),placeholder:"请选择权限组",class:"w-full"},{default:o(()=>[(f(!0),j(E,null,L($.value,t=>(f(),C(n,{key:t.id,label:t.name,value:t.id},{default:o(()=>[a("div",null,[a("div",null,i(t.name),1),a("div",pt,i(t.description),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"备注",prop:"remark"},{default:o(()=>[l(m,{modelValue:d.remark,"onUpdate:modelValue":e[14]||(e[14]=t=>d.remark=t),type:"textarea",rows:3,placeholder:"请输入备注信息（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])]),_:1},8,["modelValue"]),l(Q,{modelValue:y.value,"onUpdate:modelValue":e[25]||(e[25]=t=>y.value=t),title:"编辑子账号",width:"600px","before-close":de},{footer:o(()=>[a("div",vt,[l(B,{onClick:e[24]||(e[24]=t=>y.value=!1)},{default:o(()=>e[36]||(e[36]=[h("取消")])),_:1,__:[36]}),l(B,{type:"primary",onClick:ie,loading:I.value},{default:o(()=>e[37]||(e[37]=[h(" 保存修改 ")])),_:1,__:[37]},8,["loading"])])]),default:o(()=>[U.value?(f(),C(A,{key:0,ref_key:"editFormRef",ref:P,model:s,rules:X,"label-width":"100px",class:"space-y-4"},{default:o(()=>[l(u,{label:"域名",prop:"domain"},{default:o(()=>[l(m,{modelValue:s.domain,"onUpdate:modelValue":e[17]||(e[17]=t=>s.domain=t),placeholder:"请输入域名"},null,8,["modelValue"])]),_:1}),l(u,{label:"账号",prop:"username"},{default:o(()=>[l(m,{modelValue:s.username,"onUpdate:modelValue":e[18]||(e[18]=t=>s.username=t),placeholder:"请输入登录账号",disabled:""},null,8,["modelValue"])]),_:1}),l(u,{label:"昵称",prop:"nickname"},{default:o(()=>[l(m,{modelValue:s.nickname,"onUpdate:modelValue":e[19]||(e[19]=t=>s.nickname=t),placeholder:"请输入昵称"},null,8,["modelValue"])]),_:1}),l(u,{label:"手机号",prop:"phone"},{default:o(()=>[l(m,{modelValue:s.phone,"onUpdate:modelValue":e[20]||(e[20]=t=>s.phone=t),placeholder:"请输入手机号（可选）"},null,8,["modelValue"])]),_:1}),l(u,{label:"邮箱",prop:"email"},{default:o(()=>[l(m,{modelValue:s.email,"onUpdate:modelValue":e[21]||(e[21]=t=>s.email=t),placeholder:"请输入邮箱（可选）"},null,8,["modelValue"])]),_:1}),l(u,{label:"权限组",prop:"roleId"},{default:o(()=>[l(z,{modelValue:s.roleId,"onUpdate:modelValue":e[22]||(e[22]=t=>s.roleId=t),placeholder:"请选择权限组",class:"w-full"},{default:o(()=>[(f(!0),j(E,null,L($.value,t=>(f(),C(n,{key:t.id,label:t.name,value:t.id},{default:o(()=>[a("div",null,[a("div",null,i(t.name),1),a("div",xt,i(t.description),1)])]),_:2},1032,["label","value"]))),128))]),_:1},8,["modelValue"])]),_:1}),l(u,{label:"备注",prop:"remark"},{default:o(()=>[l(m,{modelValue:s.remark,"onUpdate:modelValue":e[23]||(e[23]=t=>s.remark=t),type:"textarea",rows:3,placeholder:"请输入备注信息（可选）"},null,8,["modelValue"])]),_:1})]),_:1},8,["model"])):Ve("",!0)]),_:1},8,["modelValue"])])}}});export{Vt as default};
