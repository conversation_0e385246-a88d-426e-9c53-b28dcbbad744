import{d as G,e as H,r as m,q as O,j as l,h as g,a as e,g as a,C as L,t as n,E as V,o as v,_ as J,c as _,k as X,n as Q,M as F,F as q,L as le,b as Y,Q as te,f as de,p as ne,v as ie}from"./index-D_iwS02E.js";const ue={class:"p-6 space-y-4"},ce={class:"flex justify-between items-center"},me={class:"flex space-x-3"},pe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ge={class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},xe={class:"flex items-center space-x-3"},ke={class:"font-medium text-gray-900 dark:text-dark-text"},ve={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},be={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},fe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},he={class:"flex justify-center"},we={class:"flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50 space-x-3"},ye=["disabled"],_e=G({__name:"TemplateSelector",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select"],setup(Z,{emit:D}){const B=Z,T=D,S=H({get:()=>B.modelValue,set:r=>T("update:modelValue",r)}),h=m(""),f=m(""),C=m(1),$=m(10),k=m([]),y=m(),p=m([]),z=m([{id:"TPL001",name:"Amazon商品刊登模板.xlsx",description:"亚马逊标准商品刊登模板，包含所有必填字段",platform:"亚马逊",size:45678,updateTime:"2024-01-15",url:"/templates/amazon-listing.xlsx"},{id:"TPL002",name:"Temu批量上传模板.csv",description:"Temu平台批量商品上传模板",platform:"Temu",size:23456,updateTime:"2024-01-14",url:"/templates/temu-bulk-upload.csv"},{id:"TPL003",name:"Shein服装类目模板.xlsm",description:"Shein服装类目专用刊登模板，支持多SKU",platform:"Shein",size:67890,updateTime:"2024-01-13",url:"/templates/shein-clothing.xlsm"},{id:"TPL004",name:"通用商品信息模板.xlsx",description:"适用于多平台的通用商品信息模板",platform:"通用",size:34567,updateTime:"2024-01-12",url:"/templates/universal-product.xlsx"},{id:"TPL005",name:"Amazon变体商品模板.csv",description:"亚马逊变体商品专用模板，支持颜色尺寸变体",platform:"亚马逊",size:56789,updateTime:"2024-01-11",url:"/templates/amazon-variants.csv"}]),U=H(()=>{let r=z.value;h.value&&(r=r.filter(s=>s.name.toLowerCase().includes(h.value.toLowerCase())||s.description.toLowerCase().includes(h.value.toLowerCase())||s.platform.toLowerCase().includes(h.value.toLowerCase()))),f.value&&(r=r.filter(s=>s.platform===f.value));const t=(C.value-1)*$.value,u=t+$.value;return r.slice(t,u)}),I=r=>{k.value=r},N=r=>{if(r.raw){const t={id:"UPLOAD_"+Date.now(),name:r.name,description:"用户上传的模板文件",platform:"通用",size:r.size||0,updateTime:new Date().toLocaleDateString(),url:URL.createObjectURL(r.raw)};z.value.unshift(t),k.value.push(t),V.success(`模板文件 ${r.name} 上传成功`)}},R=r=>{const t=z.value.findIndex(s=>s.name===r.name);t>-1&&z.value.splice(t,1);const u=k.value.findIndex(s=>s.name===r.name);u>-1&&k.value.splice(u,1)},A=r=>{V.info(`预览模板：${r.name}`)},K=r=>r<1024?r+" B":r<1024*1024?(r/1024).toFixed(1)+" KB":(r/(1024*1024)).toFixed(1)+" MB",E=()=>{if(k.value.length===0){V.warning("请选择至少一个模板文件");return}T("select",k.value),j()},j=()=>{k.value=[],h.value="",f.value="",C.value=1,p.value=[],y.value&&y.value.clearFiles(),T("update:modelValue",!1)};return(r,t)=>{const u=g("el-upload"),s=g("el-input"),w=g("el-option"),P=g("el-select"),M=g("el-table-column"),W=g("el-tag"),i=g("el-button"),x=g("el-table"),o=g("el-pagination"),d=g("el-dialog");return v(),O(d,{modelValue:S.value,"onUpdate:modelValue":t[4]||(t[4]=b=>S.value=b),width:"800px","align-center":"","show-close":!1,class:"modern-dialog"},{header:l(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[t[6]||(t[6]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-green-500 to-green-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"选择模板文件"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"从模板库中选择适合的刊登模板")])],-1)),e("button",{onClick:j,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},t[5]||(t[5]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:l(()=>[e("div",we,[e("button",{onClick:j,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 取消 "),e("button",{onClick:E,disabled:k.value.length===0,class:"px-6 py-2.5 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"}," 确认选择 ("+n(k.value.length)+") ",9,ye)])]),default:l(()=>[e("div",ue,[e("div",ce,[e("div",me,[a(u,{ref_key:"uploadRef",ref:y,"file-list":p.value,"on-change":N,"on-remove":R,"auto-upload":!1,accept:".csv,.xls,.xlsx,.xlsm","show-file-list":!1},{default:l(()=>t[7]||(t[7]=[e("button",{class:"inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[e("svg",{class:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})]),L(" 上传模板 ")],-1)])),_:1,__:[7]},8,["file-list"]),a(s,{modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=b=>h.value=b),placeholder:"搜索模板文件...",style:{width:"250px"},clearable:""},{prefix:l(()=>t[8]||(t[8]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),a(P,{modelValue:f.value,"onUpdate:modelValue":t[1]||(t[1]=b=>f.value=b),placeholder:"选择平台",style:{width:"120px"},clearable:""},{default:l(()=>[a(w,{label:"全部",value:""}),a(w,{label:"亚马逊",value:"亚马逊"}),a(w,{label:"Temu",value:"Temu"}),a(w,{label:"Shein",value:"Shein"}),a(w,{label:"通用",value:"通用"})]),_:1},8,["modelValue"])]),e("div",pe," 已选择 "+n(k.value.length)+" 个文件 ",1)]),e("div",ge,[a(x,{data:U.value,onSelectionChange:I,"max-height":"400"},{default:l(()=>[a(M,{type:"selection",width:"55"}),a(M,{label:"文件名","min-width":"200"},{default:l(b=>[e("div",xe,[t[9]||(t[9]=e("svg",{class:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),e("div",null,[e("div",ke,n(b.row.name),1),e("div",ve,n(b.row.description),1)])])]),_:1}),a(M,{prop:"platform",label:"适用平台",width:"120"},{default:l(b=>[a(W,{size:"small"},{default:l(()=>[L(n(b.row.platform),1)]),_:2},1024)]),_:1}),a(M,{prop:"size",label:"文件大小",width:"100"},{default:l(b=>[e("span",be,n(K(b.row.size)),1)]),_:1}),a(M,{prop:"updateTime",label:"更新时间",width:"150"},{default:l(b=>[e("span",fe,n(b.row.updateTime),1)]),_:1}),a(M,{label:"操作",width:"100"},{default:l(b=>[a(i,{type:"primary",link:"",onClick:ee=>A(b.row)},{default:l(()=>t[10]||(t[10]=[L(" 预览 ")])),_:2,__:[10]},1032,["onClick"])]),_:1})]),_:1},8,["data"])]),e("div",he,[a(o,{"current-page":C.value,"onUpdate:currentPage":t[2]||(t[2]=b=>C.value=b),"page-size":$.value,"onUpdate:pageSize":t[3]||(t[3]=b=>$.value=b),"page-sizes":[10,20,50],total:U.value.length,layout:"sizes, prev, pager, next",small:""},null,8,["current-page","page-size","total"])])])]),_:1},8,["modelValue"])}}}),Ce=J(_e,[["__scopeId","data-v-74913519"]]),$e={class:"p-6 space-y-4"},Ve={class:"flex justify-between items-center"},Te={class:"flex space-x-3"},Pe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},je={class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},Se={class:"grid grid-cols-5 gap-4 max-h-96 overflow-y-auto"},ze=["onClick"],Me={key:0,class:"absolute top-2 right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center"},Le={class:"space-y-1"},Be=["title"],Ie={class:"text-xs text-gray-500 dark:text-dark-text-secondary truncate"},De={class:"flex items-center justify-between"},Ue={class:"text-sm font-medium text-green-600 dark:text-green-400"},Fe={class:"flex justify-between items-center"},Oe={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},He={class:"flex items-center justify-between p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Ne={class:"flex space-x-3"},Re=["disabled"],Ae=G({__name:"PodProductSelector",props:{modelValue:{type:Boolean}},emits:["update:modelValue","select"],setup(Z,{emit:D}){const B=Z,T=D,S=H({get:()=>B.modelValue,set:r=>T("update:modelValue",r)}),h=m(""),f=m(""),C=m(1),$=m(15),k=m([]),y=m([{id:"POD001",name:"个性化T恤 - 猫咪图案",baseProduct:"纯棉圆领T恤",minPrice:79.9,skuCount:6,coverImage:"https://picsum.photos/400/400?random=101",publishStatus:"published",creator:"张三",createTime:"2024-01-15 14:30:25"},{id:"POD002",name:"定制马克杯 - 风景图案",baseProduct:"陶瓷马克杯",minPrice:45.9,skuCount:3,coverImage:"https://picsum.photos/400/400?random=102",publishStatus:"unpublished",creator:"李四",createTime:"2024-01-15 13:45:12"},{id:"POD003",name:"艺术帆布包 - 抽象图案",baseProduct:"帆布手提袋",minPrice:89.9,skuCount:4,coverImage:"https://picsum.photos/400/400?random=103",publishStatus:"publishing",creator:"王五",createTime:"2024-01-15 12:20:08"},{id:"POD004",name:"个性化手机壳 - 几何图案",baseProduct:"iPhone手机壳",minPrice:39.9,skuCount:8,coverImage:"https://picsum.photos/400/400?random=104",publishStatus:"published",creator:"赵六",createTime:"2024-01-15 11:15:33"},{id:"POD005",name:"定制抱枕 - 卡通图案",baseProduct:"方形抱枕",minPrice:69.9,skuCount:5,coverImage:"https://picsum.photos/400/400?random=105",publishStatus:"unpublished",creator:"钱七",createTime:"2024-01-15 10:30:45"},{id:"POD006",name:"创意鼠标垫 - 星空图案",baseProduct:"橡胶鼠标垫",minPrice:29.9,skuCount:2,coverImage:"https://picsum.photos/400/400?random=106",publishStatus:"published",creator:"孙八",createTime:"2024-01-15 09:20:15"},{id:"POD007",name:"个性化水杯 - 励志文字",baseProduct:"不锈钢保温杯",minPrice:59.9,skuCount:4,coverImage:"https://picsum.photos/400/400?random=107",publishStatus:"unpublished",creator:"周九",createTime:"2024-01-15 08:15:30"},{id:"POD008",name:"定制笔记本 - 花卉图案",baseProduct:"A5线装笔记本",minPrice:35.9,skuCount:3,coverImage:"https://picsum.photos/400/400?random=108",publishStatus:"publishing",creator:"吴十",createTime:"2024-01-15 07:10:45"}]),p=H(()=>{let r=y.value;return h.value&&(r=r.filter(t=>t.name.toLowerCase().includes(h.value.toLowerCase())||t.baseProduct.toLowerCase().includes(h.value.toLowerCase()))),f.value&&(r=r.filter(t=>t.publishStatus===f.value)),r}),z=H(()=>{const r=(C.value-1)*$.value,t=r+$.value;return p.value.slice(r,t)}),U=r=>k.value.some(t=>t.id===r),I=r=>{const t=k.value.findIndex(u=>u.id===r.id);t>-1?k.value.splice(t,1):k.value.push(r)},N=()=>{k.value=[...p.value]},R=()=>{k.value=[]},A=r=>{const t={published:"bg-green-100 text-green-800",publishing:"bg-yellow-100 text-yellow-800",unpublished:"bg-gray-100 text-gray-800"};return t[r]||t.unpublished},K=r=>({published:"已刊登",publishing:"刊登中",unpublished:"未刊登"})[r]||"未知",E=()=>{if(k.value.length===0){V.warning("请选择至少一个POD商品");return}T("select",k.value),j()},j=()=>{k.value=[],h.value="",f.value="",C.value=1,T("update:modelValue",!1)};return(r,t)=>{const u=g("el-input"),s=g("el-option"),w=g("el-select"),P=g("el-image"),M=g("el-pagination"),W=g("el-dialog");return v(),O(W,{modelValue:S.value,"onUpdate:modelValue":t[4]||(t[4]=i=>S.value=i),width:"1000px","align-center":"","show-close":!1,class:"modern-dialog"},{header:l(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[t[6]||(t[6]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"选择POD商品"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"选择要批量刊登的POD商品")])],-1)),e("button",{onClick:j,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},t[5]||(t[5]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:l(()=>[e("div",He,[e("div",{class:"flex space-x-2"},[e("button",{onClick:N,class:"px-4 py-2 text-sm text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 全选 "),e("button",{onClick:R,class:"px-4 py-2 text-sm text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 清空 ")]),e("div",Ne,[e("button",{onClick:j,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 取消 "),e("button",{onClick:E,disabled:k.value.length===0,class:"px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"}," 确认选择 ("+n(k.value.length)+") ",9,Re)])])]),default:l(()=>[e("div",$e,[e("div",Ve,[e("div",Te,[a(u,{modelValue:h.value,"onUpdate:modelValue":t[0]||(t[0]=i=>h.value=i),placeholder:"搜索商品名称...",style:{width:"250px"},clearable:""},{prefix:l(()=>t[7]||(t[7]=[e("svg",{class:"w-4 h-4 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)])),_:1},8,["modelValue"]),a(w,{modelValue:f.value,"onUpdate:modelValue":t[1]||(t[1]=i=>f.value=i),placeholder:"刊登状态",style:{width:"120px"},clearable:""},{default:l(()=>[a(s,{label:"全部",value:""}),a(s,{label:"未刊登",value:"unpublished"}),a(s,{label:"已刊登",value:"published"}),a(s,{label:"刊登中",value:"publishing"})]),_:1},8,["modelValue"])]),e("div",Pe," 已选择 "+n(k.value.length)+" 个商品 ",1)]),e("div",je,[e("div",Se,[(v(!0),_(q,null,X(z.value,i=>(v(),_("div",{key:i.id,class:Q(["border-2 rounded-lg p-3 cursor-pointer transition-all duration-200 hover:shadow-md relative",U(i.id)?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-dark-border hover:border-blue-300"]),onClick:x=>I(i)},[U(i.id)?(v(),_("div",Me,t[8]||(t[8]=[e("svg",{class:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})],-1)]))):F("",!0),a(P,{src:i.coverImage,fit:"cover",class:"w-full h-24 rounded mb-2"},null,8,["src"]),e("div",Le,[e("div",{class:"text-sm font-medium text-gray-900 dark:text-dark-text truncate",title:i.name},n(i.name),9,Be),e("div",Ie,n(i.baseProduct),1),e("div",De,[e("span",Ue," ¥"+n(i.minPrice),1),e("span",{class:Q([A(i.publishStatus),"inline-flex items-center px-1.5 py-0.5 rounded text-xs font-medium"])},n(K(i.publishStatus)),3)])])],10,ze))),128))])]),e("div",Fe,[e("div",Oe," 共 "+n(p.value.length)+" 个商品 ",1),a(M,{"current-page":C.value,"onUpdate:currentPage":t[2]||(t[2]=i=>C.value=i),"page-size":$.value,"onUpdate:pageSize":t[3]||(t[3]=i=>$.value=i),"page-sizes":[15,30,45],total:p.value.length,layout:"sizes, prev, pager, next",small:""},null,8,["current-page","page-size","total"])])])]),_:1},8,["modelValue"])}}}),Ke=J(Ae,[["__scopeId","data-v-19f080fe"]]),Ee={class:"p-6 space-y-6"},We={class:"flex justify-start space-x-3"},Ze={key:0,class:"space-y-3"},qe={class:"grid grid-cols-1 gap-3"},Qe={class:"flex items-center space-x-3"},Ge={class:"font-medium text-gray-900 dark:text-dark-text"},Je={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Xe={class:"space-y-3"},Ye={class:"flex items-center justify-between"},et={key:0,class:"grid grid-cols-4 gap-4 max-h-48 overflow-y-auto"},tt={class:"mt-1 text-xs text-gray-600 dark:text-dark-text-secondary truncate"},rt={key:0,class:"flex items-center justify-center border border-gray-200 dark:border-dark-border rounded-lg p-2"},at={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},ot={class:"flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50 space-x-3"},st=["disabled"],lt=G({__name:"CreateListingDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(Z,{emit:D}){const B=Z,T=D,S=H({get:()=>B.modelValue,set:u=>T("update:modelValue",u)}),h=m(),f=m(!1),C=m(!1),$=m(!1),k=m([]),y=m([]),p=m([]),z=H(()=>y.value.length>0&&p.value.length>0),U=u=>{if(u.raw){const s={name:u.name,size:u.size||0};y.value.push(s)}},I=u=>{const s=y.value.findIndex(w=>w.name===u.name);s>-1&&y.value.splice(s,1)},N=u=>{const s=y.value.findIndex(w=>w.name===u.name);s>-1&&y.value.splice(s,1)},R=u=>{y.value=[...y.value,...u]},A=u=>{p.value=u},K=u=>{const s=p.value.findIndex(w=>w.id===u.id);s>-1&&p.value.splice(s,1)},E=u=>u<1024?u+" B":u<1024*1024?(u/1024).toFixed(1)+" KB":(u/(1024*1024)).toFixed(1)+" MB",j=async()=>{if(z.value){f.value=!0;try{await new Promise(u=>setTimeout(u,2e3)),V.success("刊登任务创建成功！"),T("success"),r()}catch{V.error("创建失败，请重试")}finally{f.value=!1}}},r=()=>{t(),T("update:modelValue",!1)},t=()=>{y.value=[],p.value=[],k.value=[],h.value&&h.value.clearFiles()};return(u,s)=>{const w=g("el-button"),P=g("el-upload"),M=g("el-image"),W=g("el-dialog");return v(),_(q,null,[a(W,{modelValue:S.value,"onUpdate:modelValue":s[2]||(s[2]=i=>S.value=i),width:"900px","align-center":"","before-close":r,"show-close":!1,class:"modern-dialog",onClose:t},{header:l(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[s[6]||(s[6]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"新建刊登任务"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"配置刊登平台、模板文件和商品信息")])],-1)),e("button",{onClick:r,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},s[5]||(s[5]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:l(()=>[e("div",ot,[e("button",{onClick:r,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 取消 "),e("button",{onClick:j,disabled:!z.value||f.value,class:"px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"},n(f.value?"生成中...":"生成刊登数据"),9,st)])]),default:l(()=>[e("div",Ee,[e("div",We,[a(P,{ref_key:"uploadRef",ref:h,"file-list":k.value,"on-change":U,"on-remove":I,"auto-upload":!1,accept:".csv,.xls,.xlsx,.xlsm","show-file-list":!1},{default:l(()=>[a(w,{type:"primary",size:"large"},{default:l(()=>s[7]||(s[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),L(" 上传模板文件 ")])),_:1,__:[7]})]),_:1},8,["file-list"]),a(w,{size:"large",onClick:s[0]||(s[0]=i=>C.value=!0)},{default:l(()=>s[8]||(s[8]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})],-1),L(" 选择模板文件 ")])),_:1,__:[8]})]),y.value.length>0?(v(),_("div",Ze,[s[11]||(s[11]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"已选择的模板文件",-1)),e("div",qe,[(v(!0),_(q,null,X(y.value,i=>(v(),_("div",{key:i.name,class:"flex items-center justify-between p-3 bg-gray-50 dark:bg-dark-card rounded-lg border border-gray-200 dark:border-dark-border"},[e("div",Qe,[s[9]||(s[9]=e("svg",{class:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),e("div",null,[e("div",Ge,n(i.name),1),e("div",Je,n(E(i.size)),1)])]),a(w,{type:"danger",link:"",onClick:x=>N(i)},{default:l(()=>s[10]||(s[10]=[L(" 删除 ")])),_:2,__:[10]},1032,["onClick"])]))),128))])])):F("",!0),e("div",Xe,[e("div",Ye,[s[12]||(s[12]=e("h4",{class:"text-sm font-medium text-gray-900 dark:text-dark-text"},"选择POD商品",-1)),e("button",{onClick:s[1]||(s[1]=i=>$.value=!0),class:"px-6 py-2.5 bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"}," 选择商品 ("+n(p.value.length)+") ",1)]),p.value.length>0?(v(),_("div",et,[(v(!0),_(q,null,X(p.value.slice(0,8),i=>(v(),_("div",{key:i.id,class:"relative border border-gray-200 dark:border-dark-border rounded-lg p-2"},[a(M,{src:i.coverImage,fit:"cover",class:"w-full h-20 rounded"},null,8,["src"]),e("div",tt,n(i.name),1),a(w,{type:"danger",link:"",size:"small",class:"absolute top-1 right-1",onClick:x=>K(i)},{default:l(()=>s[13]||(s[13]=[L(" × ")])),_:2,__:[13]},1032,["onClick"])]))),128)),p.value.length>8?(v(),_("div",rt,[e("span",at," +"+n(p.value.length-8),1)])):F("",!0)])):F("",!0)])])]),_:1},8,["modelValue"]),a(Ce,{modelValue:C.value,"onUpdate:modelValue":s[3]||(s[3]=i=>C.value=i),onSelect:R},null,8,["modelValue"]),a(Ke,{modelValue:$.value,"onUpdate:modelValue":s[4]||(s[4]=i=>$.value=i),onSelect:A},null,8,["modelValue"])],64)}}}),dt=J(lt,[["__scopeId","data-v-9ee1b0de"]]),nt={key:0,class:"p-6 space-y-6"},it={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},ut={class:"grid grid-cols-2 gap-4"},ct={class:"space-y-3"},mt={class:"flex justify-between"},pt={class:"font-medium text-gray-900 dark:text-dark-text"},gt={class:"flex justify-between"},xt={class:"font-medium text-gray-900 dark:text-dark-text"},kt={class:"flex justify-between"},vt={class:"font-medium text-gray-900 dark:text-dark-text"},bt={class:"flex justify-between"},ft={class:"font-medium text-green-600 dark:text-green-400"},ht={class:"space-y-3"},wt={class:"flex justify-between"},yt={class:"flex justify-between"},_t={class:"font-medium text-gray-900 dark:text-dark-text"},Ct={class:"flex justify-between"},$t={class:"font-medium text-gray-900 dark:text-dark-text"},Vt={class:"flex justify-between"},Tt={class:"font-medium text-blue-600 dark:text-blue-400"},Pt={class:"flex justify-between items-center mb-4"},jt={class:"flex space-x-2"},St={class:"border border-gray-200 dark:border-dark-border rounded-lg overflow-hidden"},zt={class:"flex items-center space-x-3"},Mt={class:"font-medium text-gray-900 dark:text-dark-text"},Lt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Bt={class:"font-medium text-green-600 dark:text-green-400"},It={key:0,class:"font-mono text-sm text-gray-900 dark:text-dark-text"},Dt={key:1,class:"text-gray-400"},Ut={key:0,class:"text-red-600 dark:text-red-400 text-sm"},Ft={key:1,class:"text-gray-400"},Ot={key:0,class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ht={key:1,class:"text-gray-400"},Nt={class:"flex justify-center mt-4"},Rt={class:"flex items-center justify-end p-6 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50 space-x-3"},At=G({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(Z,{emit:D}){const B=Z,T=D,S=H({get:()=>B.modelValue,set:r=>T("update:modelValue",r)}),h=m(!1),f=m(""),C=m(1),$=m(10),k=m([]);le(()=>B.task,r=>{r&&p(r.id)},{immediate:!0});const y=H(()=>{let r=k.value;f.value&&(r=r.filter(s=>s.status===f.value));const t=(C.value-1)*$.value,u=t+$.value;return r.slice(t,u)}),p=async r=>{h.value=!0;try{await new Promise(t=>setTimeout(t,500)),k.value=[{id:"1",productName:"个性化T恤 - 猫咪图案",productImage:"https://picsum.photos/400/400?random=201",sku:"POD001-S-RED",price:79.9,status:"success",platformProductId:"AMZ123456789",listingTime:"2024-01-15 14:35:20"},{id:"2",productName:"个性化T恤 - 猫咪图案",productImage:"https://picsum.photos/400/400?random=201",sku:"POD001-M-BLUE",price:79.9,status:"failed",errorMessage:"商品标题过长，超出平台限制"},{id:"3",productName:"定制马克杯 - 风景图案",productImage:"https://picsum.photos/400/400?random=202",sku:"POD002-WHITE",price:45.9,status:"success",platformProductId:"AMZ987654321",listingTime:"2024-01-15 14:36:15"}]}catch{V.error("加载任务详情失败")}finally{h.value=!1}},z=r=>{const t={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",partial_failed:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return t[r]||t.pending},U=r=>({completed:"已完成",processing:"处理中",failed:"失败",partial_failed:"部分失败",pending:"等待中"})[r]||"未知",I=r=>({success:"bg-green-100 text-green-800",failed:"bg-red-100 text-red-800"})[r]||"",N=r=>({success:"成功",failed:"失败"})[r]||"未知",R=r=>{V.success(`正在重试刊登商品：${r.productName}`)},A=r=>{V.info(`跳转到平台查看商品：${r.platformProductId}`)},K=()=>{V.success("正在导出刊登结果...")},E=()=>{var r;V.success(`正在重试任务：${(r=B.task)==null?void 0:r.id}`)},j=()=>{f.value="",C.value=1,T("update:modelValue",!1)};return(r,t)=>{const u=g("el-option"),s=g("el-select"),w=g("el-button"),P=g("el-table-column"),M=g("el-image"),W=g("el-table"),i=g("el-pagination"),x=g("el-dialog"),o=te("loading");return v(),O(x,{modelValue:S.value,"onUpdate:modelValue":t[3]||(t[3]=d=>S.value=d),width:"1000px","align-center":"","before-close":j,"show-close":!1,class:"modern-dialog"},{header:l(()=>[e("div",{class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},[t[5]||(t[5]=e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])]),e("div",null,[e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"刊登任务详情"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary"},"查看任务信息和刊登结果")])],-1)),e("button",{onClick:j,class:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"},t[4]||(t[4]=[e("svg",{class:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]),footer:l(()=>[e("div",Rt,[e("button",{onClick:j,class:"px-6 py-2.5 text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border transition-all duration-200"}," 关闭 "),r.task&&(r.task.status==="failed"||r.task.status==="partial_failed")?(v(),_("button",{key:0,onClick:E,class:"px-6 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"}," 重试任务 ")):F("",!0)])]),default:l(()=>[r.task?(v(),_("div",nt,[e("div",it,[t[14]||(t[14]=e("h4",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"任务信息",-1)),e("div",ut,[e("div",ct,[e("div",mt,[t[6]||(t[6]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"任务ID:",-1)),e("span",pt,n(r.task.id),1)]),e("div",gt,[t[7]||(t[7]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"刊登平台:",-1)),e("span",xt,n(r.task.platform),1)]),e("div",kt,[t[8]||(t[8]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"商品数量:",-1)),e("span",vt,n(r.task.productCount),1)]),e("div",bt,[t[9]||(t[9]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"成功数量:",-1)),e("span",ft,n(r.task.successCount),1)])]),e("div",ht,[e("div",wt,[t[10]||(t[10]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"任务状态:",-1)),e("span",{class:Q([z(r.task.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},n(U(r.task.status)),3)]),e("div",yt,[t[11]||(t[11]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"操作人:",-1)),e("span",_t,n(r.task.operator),1)]),e("div",Ct,[t[12]||(t[12]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"创建时间:",-1)),e("span",$t,n(r.task.createTime),1)]),e("div",Vt,[t[13]||(t[13]=e("span",{class:"text-gray-600 dark:text-dark-text-secondary"},"成功率:",-1)),e("span",Tt,n((r.task.successCount/r.task.productCount*100).toFixed(1))+"% ",1)])])])]),e("div",null,[e("div",Pt,[t[16]||(t[16]=e("h4",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"刊登结果",-1)),e("div",jt,[a(s,{modelValue:f.value,"onUpdate:modelValue":t[0]||(t[0]=d=>f.value=d),placeholder:"状态筛选",style:{width:"120px"},size:"small"},{default:l(()=>[a(u,{label:"全部",value:""}),a(u,{label:"成功",value:"success"}),a(u,{label:"失败",value:"failed"})]),_:1},8,["modelValue"]),a(w,{size:"small",onClick:K},{default:l(()=>t[15]||(t[15]=[L("导出结果")])),_:1,__:[15]})])]),e("div",St,[Y((v(),O(W,{data:y.value,"max-height":"400"},{default:l(()=>[a(P,{type:"index",label:"序号",width:"60"}),a(P,{label:"商品信息","min-width":"200"},{default:l(d=>[e("div",zt,[a(M,{src:d.row.productImage,fit:"cover",class:"w-12 h-12 rounded border border-gray-200 dark:border-dark-border"},null,8,["src"]),e("div",null,[e("div",Mt,n(d.row.productName),1),e("div",Lt,"SKU: "+n(d.row.sku),1)])])]),_:1}),a(P,{prop:"price",label:"价格",width:"100"},{default:l(d=>[e("span",Bt," ¥"+n(d.row.price),1)]),_:1}),a(P,{prop:"status",label:"刊登状态",width:"120"},{default:l(d=>[e("span",{class:Q([I(d.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},n(N(d.row.status)),3)]),_:1}),a(P,{prop:"platformProductId",label:"平台商品ID",width:"150"},{default:l(d=>[d.row.platformProductId?(v(),_("span",It,n(d.row.platformProductId),1)):(v(),_("span",Dt,"-"))]),_:1}),a(P,{prop:"errorMessage",label:"错误信息","min-width":"200"},{default:l(d=>[d.row.errorMessage?(v(),_("span",Ut,n(d.row.errorMessage),1)):(v(),_("span",Ft,"-"))]),_:1}),a(P,{prop:"listingTime",label:"刊登时间",width:"150"},{default:l(d=>[d.row.listingTime?(v(),_("span",Ot,n(d.row.listingTime),1)):(v(),_("span",Ht,"-"))]),_:1}),a(P,{label:"操作",width:"100"},{default:l(d=>[d.row.status==="failed"?(v(),O(w,{key:0,type:"primary",link:"",size:"small",onClick:b=>R(d.row)},{default:l(()=>t[17]||(t[17]=[L(" 重试 ")])),_:2,__:[17]},1032,["onClick"])):d.row.platformProductId?(v(),O(w,{key:1,type:"primary",link:"",size:"small",onClick:b=>A(d.row)},{default:l(()=>t[18]||(t[18]=[L(" 查看 ")])),_:2,__:[18]},1032,["onClick"])):F("",!0)]),_:1})]),_:1},8,["data"])),[[o,h.value]])]),e("div",Nt,[a(i,{"current-page":C.value,"onUpdate:currentPage":t[1]||(t[1]=d=>C.value=d),"page-size":$.value,"onUpdate:pageSize":t[2]||(t[2]=d=>$.value=d),"page-sizes":[10,20,50],total:y.value.length,layout:"sizes, prev, pager, next",small:""},null,8,["current-page","page-size","total"])])])])):F("",!0)]),_:1},8,["modelValue"])}}}),Kt=J(At,[["__scopeId","data-v-8c44212c"]]),Et={class:"space-y-6"},Wt={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},Zt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},qt={class:"flex items-center justify-between"},Qt={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},Gt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},Jt={class:"flex items-center justify-between"},Xt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Yt={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},er={class:"flex items-center justify-between"},tr={class:"text-2xl font-bold text-blue-600 dark:text-blue-400"},rr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ar={class:"flex items-center justify-between"},or={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},sr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},lr={class:"flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0"},dr={class:"flex items-center space-x-3"},nr={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},ir={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},ur={class:"relative"},cr={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},mr={class:"overflow-x-auto"},pr={class:"font-medium text-gray-900 dark:text-dark-text"},gr={class:"flex items-center"},xr={class:"font-medium text-gray-900 dark:text-dark-text"},kr={class:"flex items-center"},vr={class:"font-medium text-gray-900 dark:text-dark-text"},br={class:"flex items-center"},fr={class:"font-medium text-green-600 dark:text-green-400"},hr={class:"text-gray-500 dark:text-dark-text-secondary ml-1"},wr={class:"text-gray-700 dark:text-dark-text-secondary"},yr={class:"text-gray-700 dark:text-dark-text-secondary"},_r={class:"text-gray-700 dark:text-dark-text-secondary"},Cr={class:"flex items-center space-x-2"},$r=["onClick"],Vr={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Tr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Pr=G({__name:"index",setup(Z){const D=m(!1),B=m(!1),T=m(!1),S=m(null),h=m([]),f=m(""),C=m(86),$=m(92.5),k=m(12),y=m(3),p=m({currentPage:1,pageSize:10,total:0}),z=m([{id:"LST001",platform:"亚马逊",productCount:25,successCount:23,status:"completed",operator:"张三",templateName:"Amazon商品刊登模板.xlsx",createTime:"2024-01-15 14:30:25"},{id:"LST002",platform:"Temu",productCount:18,successCount:15,status:"partial_failed",operator:"李四",templateName:"Temu批量上传模板.csv",createTime:"2024-01-15 13:45:12"},{id:"LST003",platform:"Shein",productCount:30,successCount:0,status:"processing",operator:"王五",templateName:"Shein服装类目模板.xlsm",createTime:"2024-01-15 12:20:08"},{id:"LST004",platform:"亚马逊",productCount:12,successCount:0,status:"failed",operator:"赵六",templateName:"Amazon变体商品模板.csv",createTime:"2024-01-15 11:15:33"},{id:"LST005",platform:"Temu",productCount:8,successCount:8,status:"completed",operator:"钱七",templateName:"通用商品信息模板.xlsx",createTime:"2024-01-15 10:30:45"}]),U=H(()=>{let x=z.value;f.value&&(x=x.filter(b=>b.id.toLowerCase().includes(f.value.toLowerCase())||b.platform.toLowerCase().includes(f.value.toLowerCase())));const o=(p.value.currentPage-1)*p.value.pageSize,d=o+p.value.pageSize;return x.slice(o,d)});de(()=>{I()});const I=()=>{D.value=!0,setTimeout(()=>{p.value.total=z.value.length,D.value=!1},500)},N=x=>{const o={completed:"bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300",processing:"bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300",failed:"bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300",partial_failed:"bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300",pending:"bg-gray-100 dark:bg-gray-900/30 text-gray-800 dark:text-gray-300"};return o[x]||o.pending},R=x=>({completed:"已完成",processing:"处理中",failed:"失败",partial_failed:"部分失败",pending:"等待中"})[x]||"未知",A=x=>{S.value=x,T.value=!0},K=x=>{const{action:o,row:d}=x;switch(o){case"cancel":j(d);break;case"retry":r(d);break;case"export":t(d);break;case"download":u(d);break;default:V.warning("未知操作")}},E=()=>{p.value.currentPage=1,I()},j=x=>{V.success(`已取消任务：${x.id}`)},r=x=>{V.success(`正在重试任务：${x.id}`)},t=x=>{V.success(`正在导出任务结果：${x.id}`)},u=x=>{V.success(`正在下载刊登数据表：${x.id}`)},s=()=>{V.success("导出任务功能开发中...")},w=()=>{V.success(`正在批量取消 ${h.value.length} 个任务...`)},P=()=>{V.success("操作成功！"),I()},M=x=>{p.value.pageSize=x,p.value.currentPage=1,I()},W=x=>{p.value.currentPage=x,I()},i=x=>{h.value=x};return(x,o)=>{const d=g("el-table-column"),b=g("el-dropdown-item"),ee=g("el-dropdown-menu"),re=g("el-dropdown"),ae=g("el-table"),oe=g("el-pagination"),se=te("loading");return v(),_(q,null,[e("div",Et,[o[24]||(o[24]=ne('<div class="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800" data-v-4fc283be><div class="flex items-center space-x-3" data-v-4fc283be><div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center" data-v-4fc283be><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-4fc283be><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" data-v-4fc283be></path></svg></div><div data-v-4fc283be><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-4fc283be>批量刊登</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-4fc283be>批量发布商品到各大电商平台</p></div></div></div>',1)),e("div",Wt,[e("div",Zt,[e("div",qt,[e("div",null,[o[6]||(o[6]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"刊登任务总数",-1)),e("p",Qt,n(C.value),1)]),o[7]||(o[7]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})])],-1))])]),e("div",Gt,[e("div",Jt,[e("div",null,[o[8]||(o[8]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"成功率",-1)),e("p",Xt,n($.value)+"%",1)]),o[9]||(o[9]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",Yt,[e("div",er,[e("div",null,[o[10]||(o[10]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"今日刊登",-1)),e("p",tr,n(k.value),1)]),o[11]||(o[11]=e("div",{class:"w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",rr,[e("div",ar,[e("div",null,[o[12]||(o[12]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"处理中任务",-1)),e("p",or,n(y.value),1)]),o[13]||(o[13]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})])],-1))])])]),e("div",sr,[e("div",lr,[e("div",dr,[e("button",{onClick:o[0]||(o[0]=c=>B.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-indigo-600 hover:from-blue-600 hover:to-indigo-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},o[14]||(o[14]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})],-1),L(" 新建刊登任务 ")])),e("button",{onClick:s,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},o[15]||(o[15]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})],-1),L(" 导出任务 ")])),h.value.length>0?(v(),_("div",nr,[e("span",ir," 已选择 "+n(h.value.length)+" 项 ",1),e("button",{onClick:w,class:"inline-flex items-center px-3 py-1.5 bg-red-500 hover:bg-red-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},o[16]||(o[16]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1),L(" 批量取消 ")]))])):F("",!0)]),e("div",ur,[Y(e("input",{"onUpdate:modelValue":o[1]||(o[1]=c=>f.value=c),type:"text",placeholder:"搜索任务ID、平台...",class:"w-full sm:w-64 pl-10 pr-4 py-2 border border-gray-300 dark:border-dark-border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:bg-dark-card dark:text-dark-text",onInput:E},null,544),[[ie,f.value]]),o[17]||(o[17]=e("svg",{class:"w-5 h-5 text-gray-400 absolute left-3 top-2.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1))])])]),e("div",cr,[o[23]||(o[23]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"批量刊登任务"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理您的批量刊登任务")],-1)),e("div",mr,[Y((v(),O(ae,{data:U.value,style:{width:"100%"},onSelectionChange:i,class:"modern-table","header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"}},{default:l(()=>[a(d,{type:"selection",width:"55"}),a(d,{prop:"id",label:"任务ID",width:"100"},{default:l(c=>[e("span",pr,n(c.row.id),1)]),_:1}),a(d,{prop:"platform",label:"刊登平台",width:"120"},{default:l(c=>[e("div",gr,[e("span",xr,n(c.row.platform),1)])]),_:1}),a(d,{prop:"productCount",label:"商品数量",width:"120"},{default:l(c=>[e("div",kr,[e("span",vr,n(c.row.productCount),1)])]),_:1}),a(d,{prop:"successCount",label:"成功数量",width:"120"},{default:l(c=>[e("div",br,[e("span",fr,n(c.row.successCount),1),e("span",hr,"/ "+n(c.row.productCount),1)])]),_:1}),a(d,{prop:"status",label:"状态",width:"120"},{default:l(c=>[e("span",{class:Q([N(c.row.status),"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"])},n(R(c.row.status)),3)]),_:1}),a(d,{prop:"operator",label:"操作人",width:"120"},{default:l(c=>[e("span",wr,n(c.row.operator),1)]),_:1}),a(d,{prop:"templateName",label:"刊登模板",width:"150"},{default:l(c=>[e("span",yr,n(c.row.templateName||"-"),1)]),_:1}),a(d,{prop:"createTime",label:"创建时间",width:"180"},{default:l(c=>[e("span",_r,n(c.row.createTime),1)]),_:1}),a(d,{label:"操作",width:"180"},{default:l(c=>[e("div",Cr,[e("button",{onClick:jr=>A(c.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 bg-blue-50 dark:bg-blue-900/20 hover:bg-blue-100 dark:hover:bg-blue-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,$r),a(re,{onCommand:K,trigger:"click"},{dropdown:l(()=>[a(ee,null,{default:l(()=>[c.row.status==="processing"?(v(),O(b,{key:0,command:{action:"cancel",row:c.row}},{default:l(()=>o[18]||(o[18]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})]),e("span",null,"取消任务")],-1)])),_:2,__:[18]},1032,["command"])):F("",!0),c.row.status==="failed"||c.row.status==="partial_failed"?(v(),O(b,{key:1,command:{action:"retry",row:c.row}},{default:l(()=>o[19]||(o[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"})]),e("span",null,"重试任务")],-1)])),_:2,__:[19]},1032,["command"])):F("",!0),a(b,{command:{action:"export",row:c.row}},{default:l(()=>o[20]||(o[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4"})]),e("span",null,"导出结果")],-1)])),_:2,__:[20]},1032,["command"]),c.row.status==="completed"?(v(),O(b,{key:2,command:{action:"download",row:c.row}},{default:l(()=>o[21]||(o[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})]),e("span",null,"下载刊登数据表")],-1)])),_:2,__:[21]},1032,["command"])):F("",!0)]),_:2},1024)]),default:l(()=>[o[22]||(o[22]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[L(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[22]},1024)])]),_:1})]),_:1},8,["data"])),[[se,D.value]])]),e("div",Vr,[e("div",Tr," 共 "+n(p.value.total)+" 条记录 ",1),a(oe,{"current-page":p.value.currentPage,"onUpdate:currentPage":o[2]||(o[2]=c=>p.value.currentPage=c),"page-size":p.value.pageSize,"onUpdate:pageSize":o[3]||(o[3]=c=>p.value.pageSize=c),"page-sizes":[10,20,50,100],total:p.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:M,onCurrentChange:W,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),a(dt,{modelValue:B.value,"onUpdate:modelValue":o[4]||(o[4]=c=>B.value=c),onSuccess:P},null,8,["modelValue"]),a(Kt,{modelValue:T.value,"onUpdate:modelValue":o[5]||(o[5]=c=>T.value=c),task:S.value},null,8,["modelValue","task"])],64)}}}),zr=J(Pr,[["__scopeId","data-v-4fc283be"]]);export{zr as default};
