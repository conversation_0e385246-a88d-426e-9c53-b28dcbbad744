import{K as ge,d as se,r as h,L as Q,e as Y,c as x,F as Z,g as r,h as b,j as l,a as e,M as N,C as _,k as ne,t as n,E as T,o as m,_ as oe,q as ae,A as W,b as ie,Q as ue,f as ke,p as xe,G as pe,l as ve,n as le}from"./index-D_iwS02E.js";import{_ as be}from"./GallerySelectDialog.vue_vue_type_script_setup_true_lang-B_Pt5oDr.js";import{r as de}from"./ArrowDownTrayIcon-D4t4IQaq.js";var U=(p=>(p.NO_RISK="no_risk",p.LOW_RISK="low_risk",p.MEDIUM_RISK="medium_risk",p.HIGH_RISK="high_risk",p))(U||{}),M=(p=>(p.PENDING="pending",p.PROCESSING="processing",p.COMPLETED="completed",p.FAILED="failed",p))(M||{});const ee=ge({tasks:[{id:"CD001",imageCount:25,completedCount:25,status:"completed",operator:"admin",createTime:"2024-01-15 14:30:00",riskSummary:{noRisk:15,lowRisk:6,mediumRisk:3,highRisk:1}},{id:"CD002",imageCount:18,completedCount:12,status:"processing",operator:"admin",createTime:"2024-01-15 16:20:00"},{id:"CD003",imageCount:30,completedCount:30,status:"completed",operator:"admin",createTime:"2024-01-14 10:15:00",riskSummary:{noRisk:20,lowRisk:8,mediumRisk:2,highRisk:0}},{id:"CD004",imageCount:8,completedCount:0,status:"pending",operator:"admin",createTime:"2024-01-15 18:45:00"},{id:"CD005",imageCount:15,completedCount:0,status:"failed",operator:"admin",createTime:"2024-01-13 09:30:00"}],results:[{id:"DR001",taskId:"CD001",fileName:"product_image_001.jpg",originalImage:"https://picsum.photos/400/400?random=1",riskLevel:"high_risk",confidence:95,similarImages:[{id:"SI001",url:"https://picsum.photos/400/400?random=101",source:"shutterstock.com",similarity:95,copyright:"Shutterstock Inc."},{id:"SI002",url:"https://picsum.photos/400/400?random=102",source:"getty.com",similarity:88,copyright:"Getty Images"}],detectionTime:"2024-01-15 14:32:15"},{id:"DR002",taskId:"CD001",fileName:"product_image_002.jpg",originalImage:"https://picsum.photos/400/400?random=2",riskLevel:"medium_risk",confidence:72,similarImages:[{id:"SI003",url:"https://picsum.photos/400/400?random=103",source:"unsplash.com",similarity:72,copyright:"Unsplash License"}],detectionTime:"2024-01-15 14:32:28"},{id:"DR003",taskId:"CD001",fileName:"product_image_003.jpg",originalImage:"https://picsum.photos/400/400?random=3",riskLevel:"low_risk",confidence:45,similarImages:[{id:"SI004",url:"https://picsum.photos/400/400?random=104",source:"pexels.com",similarity:45,copyright:"Pexels License"}],detectionTime:"2024-01-15 14:32:41"},{id:"DR004",taskId:"CD001",fileName:"product_image_004.jpg",originalImage:"https://picsum.photos/400/400?random=4",riskLevel:"no_risk",confidence:15,similarImages:[],detectionTime:"2024-01-15 14:32:55"}]}),ce=p=>({no_risk:"无风险",low_risk:"低风险",medium_risk:"中风险",high_risk:"高风险"})[p],ye=p=>({pending:"等待中",processing:"检测中",completed:"已完成",failed:"失败"})[p],fe=p=>ee.results.filter(I=>I.taskId===p),he=p=>new Promise(I=>{setTimeout(()=>{const V={id:`CD${String(ee.tasks.length+1).padStart(3,"0")}`,imageCount:p,completedCount:0,status:"pending",operator:"admin",createTime:new Date().toLocaleString("zh-CN")};ee.tasks.unshift(V),I(V)},1e3)}),_e={class:"space-y-6"},we={class:"flex justify-start space-x-3"},Ce={key:0,class:"border border-gray-200 dark:border-dark-border rounded-lg p-4"},$e={class:"grid grid-cols-6 gap-4"},Re={class:"relative"},Se=["src","alt"],Ie=["onClick"],Ve=["title"],De={key:0,class:"mt-4 flex justify-center"},Te={key:1,class:"border-2 border-dashed border-gray-300 dark:border-dark-border rounded-lg p-12 text-center"},Le={key:2,class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},je={class:"grid grid-cols-2 gap-4"},Me={class:"flex justify-between items-center"},ze={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Be={class:"flex space-x-3"},Ee=se({__name:"CreateDetectionDialog",props:{modelValue:{type:Boolean}},emits:["update:modelValue","success"],setup(p,{emit:I}){const V=p,z=I,C=h(!1),D=h(!1),B=h([]),k=h([]),S=h(1),y=h(18),L=h("standard"),c=h("80");Q(()=>V.modelValue,g=>{C.value=g}),Q(C,g=>{z("update:modelValue",g)});const a=Y(()=>{const g=(S.value-1)*y.value,o=g+y.value;return k.value.slice(g,o)}),A=g=>{const o=new FileReader;o.onload=f=>{var F;const j=Date.now()+Math.random();k.value.push({id:j,name:g.name,url:(F=f.target)==null?void 0:F.result,file:g.raw})},o.readAsDataURL(g.raw)},K=g=>{const o=k.value.findIndex(f=>f.name===g.name);o>-1&&k.value.splice(o,1)},O=g=>{const o=k.value.findIndex(f=>f.id===g);if(o>-1){k.value.splice(o,1);const f=Math.ceil(k.value.length/y.value);S.value>f&&f>0&&(S.value=f)}},E=g=>{g.forEach(o=>{k.value.find(f=>f.id===o.id)||k.value.push({id:o.id,name:o.name,url:o.url||o.thumbnail})}),D.value=!1},G=g=>{S.value=g},i=async()=>{if(k.value.length===0){T.warning("请先选择图片");return}try{await he(k.value.length),T.success(`正在创建侵权检测任务，共 ${k.value.length} 张图片`),s(),z("success"),z("update:modelValue",!1)}catch{T.error("创建任务失败，请重试")}},s=()=>{k.value=[],B.value=[],S.value=1,L.value="standard",c.value="80"};return(g,o)=>{const f=b("el-button"),j=b("el-upload"),F=b("el-pagination"),P=b("el-option"),q=b("el-select"),J=b("el-dialog");return m(),x(Z,null,[r(J,{modelValue:C.value,"onUpdate:modelValue":o[5]||(o[5]=w=>C.value=w),title:"新建侵权检测任务",width:"900px","align-center":"",onClose:s},{footer:l(()=>[e("div",Me,[e("div",ze,n(k.value.length>0?`将检测 ${k.value.length} 张图片的侵权风险`:"请先选择图片"),1),e("div",Be,[r(f,{onClick:o[4]||(o[4]=w=>C.value=!1)},{default:l(()=>o[14]||(o[14]=[_("取消")])),_:1,__:[14]}),r(f,{type:"primary",onClick:i,disabled:k.value.length===0},{default:l(()=>o[15]||(o[15]=[_(" 开始检测 ")])),_:1,__:[15]},8,["disabled"])])])]),default:l(()=>[e("div",_e,[e("div",we,[r(j,{ref:"uploadRef","file-list":B.value,"on-change":A,"on-remove":K,"auto-upload":!1,multiple:"",accept:"image/*","show-file-list":!1},{default:l(()=>[r(f,{type:"primary",size:"large"},{default:l(()=>o[7]||(o[7]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})],-1),_(" 上传图片 ")])),_:1,__:[7]})]),_:1},8,["file-list"]),r(f,{size:"large",onClick:o[0]||(o[0]=w=>D.value=!0)},{default:l(()=>o[8]||(o[8]=[e("svg",{class:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),_(" 从图库选择 ")])),_:1,__:[8]})]),k.value.length>0?(m(),x("div",Ce,[e("div",$e,[(m(!0),x(Z,null,ne(a.value,w=>(m(),x("div",{key:w.id,class:"relative group"},[e("div",Re,[e("img",{src:w.url,alt:w.name,class:"w-full h-24 object-cover rounded-lg border border-gray-200 dark:border-dark-border"},null,8,Se),e("button",{onClick:u=>O(w.id),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 hover:bg-red-600 text-white rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-200"},o[9]||(o[9]=[e("svg",{class:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]),8,Ie)]),e("p",{class:"mt-2 text-xs text-gray-600 dark:text-dark-text-secondary truncate",title:w.name},n(w.name),9,Ve)]))),128))]),k.value.length>y.value?(m(),x("div",De,[r(F,{"current-page":S.value,"onUpdate:currentPage":o[1]||(o[1]=w=>S.value=w),"page-size":y.value,total:k.value.length,layout:"prev, pager, next",small:"",onCurrentChange:G},null,8,["current-page","page-size","total"])])):N("",!0)])):(m(),x("div",Te,o[10]||(o[10]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"暂无图片",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-dark-text-secondary"},"请上传图片或从图库中选择图片进行侵权检测",-1)]))),k.value.length>0?(m(),x("div",Le,[o[13]||(o[13]=e("h3",{class:"text-sm font-medium text-gray-900 dark:text-dark-text mb-3"},"检测设置",-1)),e("div",je,[e("div",null,[o[11]||(o[11]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 检测精度 ",-1)),r(q,{modelValue:L.value,"onUpdate:modelValue":o[2]||(o[2]=w=>L.value=w),style:{width:"100%"}},{default:l(()=>[r(P,{label:"标准检测",value:"standard"}),r(P,{label:"高精度检测",value:"high"}),r(P,{label:"快速检测",value:"fast"})]),_:1},8,["modelValue"])]),e("div",null,[o[12]||(o[12]=e("label",{class:"block text-sm font-medium text-gray-700 dark:text-dark-text-secondary mb-2"}," 相似度阈值 ",-1)),r(q,{modelValue:c.value,"onUpdate:modelValue":o[3]||(o[3]=w=>c.value=w),style:{width:"100%"}},{default:l(()=>[r(P,{label:"70% (宽松)",value:"70"}),r(P,{label:"80% (标准)",value:"80"}),r(P,{label:"90% (严格)",value:"90"})]),_:1},8,["modelValue"])])])])):N("",!0)])]),_:1},8,["modelValue"]),r(be,{modelValue:D.value,"onUpdate:modelValue":o[6]||(o[6]=w=>D.value=w),"theme-color":"blue",onSelect:E},null,8,["modelValue"])],64)}}}),Ne=oe(Ee,[["__scopeId","data-v-b4659679"]]),Pe={key:0,class:"space-y-6"},Ge={class:"bg-gray-50 dark:bg-dark-card rounded-lg p-4"},He={class:"flex items-start space-x-4"},Ue={class:"flex-shrink-0"},Ae={class:"flex-1 space-y-2"},Fe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Ke={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},Oe={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},We={class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},qe={key:0,class:"space-y-4"},Qe={class:"flex items-start space-x-4"},Je={class:"flex-shrink-0"},Xe={class:"flex-1"},Ye={class:"grid grid-cols-2 gap-4"},Ze={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},et={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},tt={key:0,class:"col-span-2"},rt={class:"text-sm font-medium text-gray-900 dark:text-dark-text"},st={class:"mt-3"},ot={class:"flex items-center justify-between mb-1"},at={class:"text-xs font-medium text-gray-900 dark:text-dark-text"},lt={class:"flex-shrink-0 flex flex-col space-y-2"},dt={key:1,class:"text-center py-8"},nt={class:"bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800"},it={class:"text-sm text-blue-800 dark:text-blue-200"},ut={key:0},ct={key:1},mt={key:2},gt={key:3},kt={class:"flex justify-between items-center"},xt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},pt={class:"flex space-x-3"},vt=se({__name:"SimilarImagesDialog",props:{modelValue:{type:Boolean},detectionResult:{}},emits:["update:modelValue"],setup(p,{emit:I}){const V=p,z=I,C=h(!1);Q(()=>V.modelValue,c=>{C.value=c}),Q(C,c=>{z("update:modelValue",c)});const D=c=>({[U.NO_RISK]:"success",[U.LOW_RISK]:"warning",[U.MEDIUM_RISK]:"danger",[U.HIGH_RISK]:"danger"})[c],B=c=>c>=90?"#f56565":c>=80?"#ed8936":c>=70?"#ecc94b":"#48bb78",k=c=>{window.open(c,"_blank")},S=c=>{T.success(`正在举报来自 ${c.source} 的侵权图片...`)},y=()=>{T.success("正在导出相似图片报告...")},L=()=>{C.value=!1};return(c,a)=>{const A=b("el-image"),K=b("el-tag"),O=b("el-progress"),E=b("el-button"),G=b("el-dialog");return m(),ae(G,{modelValue:C.value,"onUpdate:modelValue":a[0]||(a[0]=i=>C.value=i),title:"相似图片详情",width:"1000px","align-center":"",onClose:L},{footer:l(()=>{var i,s;return[e("div",kt,[e("div",xt,n(((s=(i=c.detectionResult)==null?void 0:i.similarImages)==null?void 0:s.length)||0)+" 张相似图片 ",1),e("div",pt,[r(E,{onClick:L},{default:l(()=>a[18]||(a[18]=[_("关闭")])),_:1,__:[18]}),r(E,{type:"primary",onClick:y},{default:l(()=>a[19]||(a[19]=[_("导出报告")])),_:1,__:[19]})])])]}),default:l(()=>{var i;return[c.detectionResult?(m(),x("div",Pe,[e("div",Ge,[a[5]||(a[5]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"原图信息",-1)),e("div",He,[e("div",Ue,[r(A,{src:c.detectionResult.originalImage,"preview-src-list":[c.detectionResult.originalImage],fit:"cover",class:"w-32 h-32 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])]),e("div",Ae,[e("div",null,[a[1]||(a[1]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"文件名：",-1)),e("span",Fe,n(c.detectionResult.fileName),1)]),e("div",null,[a[2]||(a[2]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"风险等级：",-1)),r(K,{type:D(c.detectionResult.riskLevel),size:"small"},{default:l(()=>[_(n(W(ce)(c.detectionResult.riskLevel)),1)]),_:1},8,["type"])]),e("div",null,[a[3]||(a[3]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"置信度：",-1)),e("span",Ke,n(c.detectionResult.confidence)+"%",1)]),e("div",null,[a[4]||(a[4]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"检测时间：",-1)),e("span",Oe,n(c.detectionResult.detectionTime),1)])])])]),e("div",null,[e("h3",We," 相似图片 ("+n(((i=c.detectionResult.similarImages)==null?void 0:i.length)||0)+" 张) ",1),c.detectionResult.similarImages&&c.detectionResult.similarImages.length>0?(m(),x("div",qe,[(m(!0),x(Z,null,ne(c.detectionResult.similarImages,s=>(m(),x("div",{key:s.id,class:"bg-white dark:bg-dark-surface rounded-lg border border-gray-200 dark:border-dark-border p-4"},[e("div",Qe,[e("div",Je,[r(A,{src:s.url,"preview-src-list":[s.url],fit:"cover",class:"w-24 h-24 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])]),e("div",Xe,[e("div",Ye,[e("div",null,[a[6]||(a[6]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"来源：",-1)),e("span",Ze,n(s.source),1)]),e("div",null,[a[7]||(a[7]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"相似度：",-1)),e("span",et,n(s.similarity)+"%",1)]),s.copyright?(m(),x("div",tt,[a[8]||(a[8]=e("span",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"版权信息：",-1)),e("span",rt,n(s.copyright),1)])):N("",!0)]),e("div",st,[e("div",ot,[a[9]||(a[9]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"相似度",-1)),e("span",at,n(s.similarity)+"%",1)]),r(O,{percentage:s.similarity,"stroke-width":6,"show-text":!1,color:B(s.similarity)},null,8,["percentage","color"])])]),e("div",lt,[r(E,{size:"small",onClick:g=>k(s.url)},{default:l(()=>a[10]||(a[10]=[_(" 查看原图 ")])),_:2,__:[10]},1032,["onClick"]),r(E,{size:"small",type:"primary",onClick:g=>S(s)},{default:l(()=>a[11]||(a[11]=[_(" 举报侵权 ")])),_:2,__:[11]},1032,["onClick"])])])]))),128))])):(m(),x("div",dt,a[12]||(a[12]=[e("svg",{class:"mx-auto h-12 w-12 text-gray-400 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1),e("h3",{class:"mt-2 text-sm font-medium text-gray-900 dark:text-dark-text"},"未发现相似图片",-1),e("p",{class:"mt-1 text-sm text-gray-500 dark:text-dark-text-secondary"},"该图片未检测到潜在的侵权风险",-1)])))]),e("div",nt,[a[17]||(a[17]=e("h3",{class:"text-lg font-semibold text-blue-900 dark:text-blue-300 mb-2"},"风险评估建议",-1)),e("div",it,[c.detectionResult.riskLevel==="high_risk"?(m(),x("p",ut,a[13]||(a[13]=[e("strong",null,"高风险：",-1),_("检测到高度相似的图片，建议立即停止使用该图片，或联系版权方获得授权。 ")]))):c.detectionResult.riskLevel==="medium_risk"?(m(),x("p",ct,a[14]||(a[14]=[e("strong",null,"中风险：",-1),_("检测到中度相似的图片，建议谨慎使用，可考虑进行图片修改或寻找替代图片。 ")]))):c.detectionResult.riskLevel==="low_risk"?(m(),x("p",mt,a[15]||(a[15]=[e("strong",null,"低风险：",-1),_("检测到轻微相似的图片，风险较低，但建议保持关注。 ")]))):(m(),x("p",gt,a[16]||(a[16]=[e("strong",null,"无风险：",-1),_("未检测到明显的侵权风险，可以安全使用。 ")])))])])])):N("",!0)]}),_:1},8,["modelValue"])}}}),bt=oe(vt,[["__scopeId","data-v-adf4fa82"]]),yt={class:"flex items-center justify-between p-6 border-b border-gray-100 dark:border-dark-border"},ft={class:"flex items-center space-x-3"},ht={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},_t={class:"px-6 py-4 bg-gray-50 dark:bg-dark-card border-b border-gray-100 dark:border-dark-border"},wt={class:"grid grid-cols-2 md:grid-cols-4 gap-6"},Ct={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},$t={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},Rt={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},St={class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},It={key:0,class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Vt={class:"grid grid-cols-4 gap-4"},Dt={class:"bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center"},Tt={class:"text-2xl font-bold text-green-600 dark:text-green-400"},Lt={class:"bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 text-center"},jt={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},Mt={class:"bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center"},zt={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},Bt={class:"bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center"},Et={class:"text-2xl font-bold text-red-600 dark:text-red-400"},Nt={class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},Pt={class:"flex items-center justify-between"},Gt={class:"flex items-center space-x-4"},Ht={class:"flex space-x-2"},Ut={class:"px-6 pb-6"},At={class:"bg-white dark:bg-dark-surface rounded-xl border border-gray-200 dark:border-dark-border overflow-hidden"},Ft={class:"flex justify-center"},Kt={class:"flex flex-col items-center"},Ot={class:"text-sm font-medium"},Wt={class:"text-sm"},qt={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card"},Qt={class:"text-sm text-gray-500 dark:text-dark-text-secondary"},Jt={class:"flex space-x-3"},Xt=se({__name:"ViewDetailsDialog",props:{modelValue:{type:Boolean},task:{}},emits:["update:modelValue"],setup(p,{emit:I}){const V=p,z=I,C=h(!1),D=h(!1),B=h(!1),k=h(null),S=h([]);Q(()=>V.modelValue,i=>{C.value=i,i&&V.task&&c()}),Q(C,i=>{z("update:modelValue",i)});const y=Y(()=>V.task?fe(V.task.id):[]),L=Y(()=>{let i=y.value;return S.value.length>0&&(i=i.filter(s=>S.value.includes(s.riskLevel))),i}),c=()=>{D.value=!0,setTimeout(()=>{D.value=!1},500)},a=i=>({[U.NO_RISK]:"success",[U.LOW_RISK]:"warning",[U.MEDIUM_RISK]:"danger",[U.HIGH_RISK]:"danger"})[i],A=i=>i>=80?"#f56565":i>=60?"#ed8936":i>=40?"#ecc94b":"#48bb78",K=()=>{},O=i=>{k.value=i,B.value=!0},E=()=>{T.success("正在导出检测结果...")},G=()=>{C.value=!1,S.value=[]};return(i,s)=>{const g=b("el-option"),o=b("el-select"),f=b("el-button"),j=b("el-table-column"),F=b("el-image"),P=b("el-tag"),q=b("el-progress"),J=b("el-table"),w=b("el-dialog"),u=ue("loading");return m(),x(Z,null,[r(w,{modelValue:C.value,"onUpdate:modelValue":s[1]||(s[1]=t=>C.value=t),width:"1200px","before-close":G,"show-close":!1,class:"modern-dialog"},{header:l(()=>{var t;return[e("div",yt,[e("div",ft,[s[4]||(s[4]=e("div",{class:"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])],-1)),e("div",null,[s[3]||(s[3]=e("h3",{class:"text-xl font-bold text-gray-900 dark:text-dark-text"},"侵权检测详情",-1)),e("p",ht,"任务ID: "+n((t=i.task)==null?void 0:t.id),1)])]),e("button",{onClick:G,class:"p-2 hover:bg-gray-100 dark:hover:bg-dark-hover rounded-lg transition-colors duration-200"},s[5]||(s[5]=[e("svg",{class:"w-5 h-5 text-gray-500 dark:text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))])]}),footer:l(()=>[e("div",qt,[e("div",Qt," 共 "+n(L.value.length)+" 条检测结果 ",1),e("div",Jt,[r(f,{onClick:G},{default:l(()=>s[18]||(s[18]=[_("关闭")])),_:1,__:[18]}),r(f,{type:"primary",onClick:E},{default:l(()=>s[19]||(s[19]=[_("导出详情")])),_:1,__:[19]})])])]),default:l(()=>{var t,v,$,H,te,re;return[e("div",_t,[e("div",wt,[e("div",null,[s[6]||(s[6]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"图片总数",-1)),e("p",Ct,n((t=i.task)==null?void 0:t.imageCount),1)]),e("div",null,[s[7]||(s[7]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"检测进度",-1)),e("p",$t,n((v=i.task)==null?void 0:v.completedCount)+"/"+n(($=i.task)==null?void 0:$.imageCount),1)]),e("div",null,[s[8]||(s[8]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"操作员",-1)),e("p",Rt,n((H=i.task)==null?void 0:H.operator),1)]),e("div",null,[s[9]||(s[9]=e("p",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"创建时间",-1)),e("p",St,n((te=i.task)==null?void 0:te.createTime),1)])])]),(re=i.task)!=null&&re.riskSummary?(m(),x("div",It,[s[14]||(s[14]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text mb-4"},"风险分布",-1)),e("div",Vt,[e("div",Dt,[e("div",Tt,n(i.task.riskSummary.noRisk),1),s[10]||(s[10]=e("div",{class:"text-sm text-green-600 dark:text-green-400"},"无风险",-1))]),e("div",Lt,[e("div",jt,n(i.task.riskSummary.lowRisk),1),s[11]||(s[11]=e("div",{class:"text-sm text-yellow-600 dark:text-yellow-400"},"低风险",-1))]),e("div",Mt,[e("div",zt,n(i.task.riskSummary.mediumRisk),1),s[12]||(s[12]=e("div",{class:"text-sm text-orange-600 dark:text-orange-400"},"中风险",-1))]),e("div",Bt,[e("div",Et,n(i.task.riskSummary.highRisk),1),s[13]||(s[13]=e("div",{class:"text-sm text-red-600 dark:text-red-400"},"高风险",-1))])])])):N("",!0),e("div",Nt,[e("div",Pt,[e("div",Gt,[s[15]||(s[15]=e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"检测结果",-1)),r(o,{modelValue:S.value,"onUpdate:modelValue":s[0]||(s[0]=R=>S.value=R),placeholder:"筛选风险等级",multiple:"",clearable:"",style:{width:"200px"},onChange:K},{default:l(()=>[r(g,{label:"无风险",value:"no_risk"}),r(g,{label:"低风险",value:"low_risk"}),r(g,{label:"中风险",value:"medium_risk"}),r(g,{label:"高风险",value:"high_risk"})]),_:1},8,["modelValue"])]),e("div",Ht,[r(f,{onClick:E,size:"small"},{default:l(()=>s[16]||(s[16]=[e("svg",{class:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1),_(" 导出结果 ")])),_:1,__:[16]})])])]),e("div",Ut,[e("div",At,[ie((m(),ae(J,{data:L.value,style:{width:"100%"},"max-height":"400",class:"modern-table"},{default:l(()=>[r(j,{prop:"fileName",label:"文件名",width:"200"}),r(j,{label:"原图",width:"100",align:"center"},{default:l(R=>[e("div",Ft,[r(F,{src:R.row.originalImage,"preview-src-list":[R.row.originalImage],fit:"cover",class:"w-16 h-16 rounded-lg border border-gray-200 dark:border-dark-border"},null,8,["src","preview-src-list"])])]),_:1}),r(j,{label:"风险等级",width:"120",align:"center"},{default:l(R=>[r(P,{type:a(R.row.riskLevel),size:"small"},{default:l(()=>[_(n(W(ce)(R.row.riskLevel)),1)]),_:2},1032,["type"])]),_:1}),r(j,{label:"置信度",width:"100",align:"center"},{default:l(R=>[e("div",Kt,[e("span",Ot,n(R.row.confidence)+"%",1),r(q,{percentage:R.row.confidence,"stroke-width":4,"show-text":!1,color:A(R.row.confidence),class:"w-full mt-1"},null,8,["percentage","color"])])]),_:1}),r(j,{label:"相似图片",width:"120",align:"center"},{default:l(R=>{var X;return[e("span",Wt,n(((X=R.row.similarImages)==null?void 0:X.length)||0)+" 张",1)]}),_:1}),r(j,{prop:"detectionTime",label:"检测时间",width:"160",align:"center"}),r(j,{label:"操作",width:"120",align:"center",fixed:"right"},{default:l(R=>[r(f,{type:"primary",size:"small",onClick:X=>O(R.row),disabled:!R.row.similarImages||R.row.similarImages.length===0},{default:l(()=>s[17]||(s[17]=[_(" 查看详情 ")])),_:2,__:[17]},1032,["onClick","disabled"])]),_:1})]),_:1},8,["data"])),[[u,D.value]])])])]}),_:1},8,["modelValue"]),r(bt,{modelValue:B.value,"onUpdate:modelValue":s[2]||(s[2]=t=>B.value=t),"detection-result":k.value},null,8,["modelValue","detection-result"])],64)}}}),Yt=oe(Xt,[["__scopeId","data-v-bcdd4e2a"]]),Zt={class:"space-y-6"},er={class:"grid grid-cols-1 md:grid-cols-4 gap-6"},tr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},rr={class:"flex items-center justify-between"},sr={class:"text-2xl font-bold text-gray-900 dark:text-dark-text"},or={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},ar={class:"flex items-center justify-between"},lr={class:"text-2xl font-bold text-yellow-600 dark:text-yellow-400"},dr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},nr={class:"flex items-center justify-between"},ir={class:"text-2xl font-bold text-orange-600 dark:text-orange-400"},ur={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border hover:shadow-lg dark:hover:shadow-2xl transition-all duration-300"},cr={class:"flex items-center justify-between"},mr={class:"text-2xl font-bold text-red-600 dark:text-red-400"},gr={class:"bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border"},kr={class:"flex justify-between items-center"},xr={class:"flex items-center space-x-3"},pr={key:0,class:"flex items-center space-x-2 ml-4 pl-4 border-l border-gray-200 dark:border-dark-border"},vr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},br={class:"bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden"},yr={class:"overflow-x-auto"},fr={class:"font-mono text-sm font-medium text-primary-600 dark:text-primary-400"},hr={class:"space-y-1"},_r={class:"flex items-center space-x-2"},wr={class:"font-medium text-gray-900 dark:text-dark-text"},Cr={class:"flex items-center space-x-2"},$r={class:"font-medium text-green-600 dark:text-green-400"},Rr={class:"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-1.5"},Sr={class:"space-y-2"},Ir={key:0},Vr=["onClick"],Dr={key:0,class:"flex flex-wrap gap-1"},Tr={key:0,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"},Lr={key:1,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400"},jr={key:2,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-400"},Mr={key:3,class:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"},zr={key:1,class:"text-gray-400 dark:text-dark-text-secondary"},Br={class:"font-medium text-gray-900 dark:text-dark-text"},Er={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Nr={class:"flex items-center space-x-2"},Pr=["onClick"],Gr={class:"flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50"},Hr={class:"text-sm text-gray-600 dark:text-dark-text-secondary"},Ur=se({__name:"index",setup(p){const I=h(!1),V=h(!1),z=h(!1),C=h(null),D=h([]),B=h(""),k=h([]),S=h(""),y=h({currentPage:1,pageSize:10,total:0}),L=Y(()=>ee.tasks.filter(t=>t.status===M.COMPLETED).reduce((t,v)=>(v.riskSummary&&(t.noRisk+=v.riskSummary.noRisk,t.lowRisk+=v.riskSummary.lowRisk,t.mediumRisk+=v.riskSummary.mediumRisk,t.highRisk+=v.riskSummary.highRisk),t),{noRisk:0,lowRisk:0,mediumRisk:0,highRisk:0})),c=Y(()=>{let u=ee.tasks;if(B.value&&(u=u.filter($=>$.status===B.value)),k.value.length>0&&(u=u.filter($=>$.riskSummary?k.value.some(H=>{switch(H){case"no_risk":return $.riskSummary.noRisk>0;case"low_risk":return $.riskSummary.lowRisk>0;case"medium_risk":return $.riskSummary.mediumRisk>0;case"high_risk":return $.riskSummary.highRisk>0;default:return!1}}):!1)),S.value){const $=S.value.toLowerCase();u=u.filter(H=>H.id.toLowerCase().includes($)||H.operator.toLowerCase().includes($))}y.value.total=u.length;const t=(y.value.currentPage-1)*y.value.pageSize,v=t+y.value.pageSize;return u.slice(t,v)});ke(()=>{a()});const a=()=>{I.value=!0,setTimeout(()=>{I.value=!1},500)},A=u=>{D.value=u},K=u=>{C.value=u,z.value=!0},O=u=>{const{action:t,row:v}=u;switch(t){case"titleGenerate":f(v);break;case"batchListing":j(v);break;case"smartCrop":F(v);break;case"oneClickCutout":P(v);break;case"superSplit":q(v);break}},E=u=>{const t={[M.PENDING]:"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400",[M.PROCESSING]:"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400",[M.COMPLETED]:"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400",[M.FAILED]:"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"};return t[u]||t[M.PENDING]},G=u=>{const t={[M.PENDING]:"bg-gray-400",[M.PROCESSING]:"bg-blue-400",[M.COMPLETED]:"bg-green-400",[M.FAILED]:"bg-red-400"};return t[u]||t[M.PENDING]},i=u=>{T.info(`任务 ${u.id} 失败原因：网络连接超时`)},s=()=>{T.success("导出表格功能开发中...")},g=()=>{T.success(`正在批量导出 ${D.value.length} 个任务...`)},o=()=>{T.success("侵权检测任务创建成功！"),a()},f=u=>{T.success(`正在为检测任务 ${u.id} 创建标题生成任务...`)},j=u=>{T.success(`正在为检测任务 ${u.id} 创建批量刊登任务...`)},F=u=>{T.success(`正在为检测任务 ${u.id} 创建智能裁图任务...`)},P=u=>{T.success(`正在为检测任务 ${u.id} 创建一键抠图任务...`)},q=u=>{T.success(`正在为检测任务 ${u.id} 创建超级裂变任务...`)},J=u=>{y.value.pageSize=u,y.value.currentPage=1,a()},w=u=>{y.value.currentPage=u,a()};return(u,t)=>{const v=b("el-table-column"),$=b("el-dropdown-item"),H=b("el-dropdown-menu"),te=b("el-dropdown"),re=b("el-table"),R=b("el-pagination"),X=ue("loading");return m(),x(Z,null,[e("div",Zt,[t[26]||(t[26]=xe('<div class="bg-gradient-to-r from-primary-50 to-blue-50 dark:from-primary-900/20 dark:to-blue-900/20 rounded-2xl p-6 border border-primary-100 dark:border-primary-800" data-v-d133d237><div class="flex items-center space-x-3" data-v-d133d237><div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center" data-v-d133d237><svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24" data-v-d133d237><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" data-v-d133d237></path></svg></div><div data-v-d133d237><h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text" data-v-d133d237>侵权检测</h1><p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary" data-v-d133d237>AI智能图片侵权检测和风险评估工具</p></div></div></div>',1)),e("div",er,[e("div",tr,[e("div",rr,[e("div",null,[t[5]||(t[5]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"无风险",-1)),e("p",sr,n(L.value.noRisk),1)]),t[6]||(t[6]=e("div",{class:"w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-green-600 dark:text-green-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M5 13l4 4L19 7"})])],-1))])]),e("div",or,[e("div",ar,[e("div",null,[t[7]||(t[7]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"低风险",-1)),e("p",lr,n(L.value.lowRisk),1)]),t[8]||(t[8]=e("div",{class:"w-12 h-12 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-yellow-600 dark:text-yellow-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"})])],-1))])]),e("div",dr,[e("div",nr,[e("div",null,[t[9]||(t[9]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"中风险",-1)),e("p",ir,n(L.value.mediumRisk),1)]),t[10]||(t[10]=e("div",{class:"w-12 h-12 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-orange-600 dark:text-orange-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})])],-1))])]),e("div",ur,[e("div",cr,[e("div",null,[t[11]||(t[11]=e("p",{class:"text-sm font-medium text-gray-600 dark:text-dark-text-secondary"},"高风险",-1)),e("p",mr,n(L.value.highRisk),1)]),t[12]||(t[12]=e("div",{class:"w-12 h-12 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-6 h-6 text-red-600 dark:text-red-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})])],-1))])])]),e("div",gr,[e("div",kr,[e("div",xr,[e("button",{onClick:t[0]||(t[0]=d=>V.value=!0),class:"inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-primary-500 to-primary-600 hover:from-primary-600 hover:to-primary-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105"},[r(W(pe),{class:"w-5 h-5 mr-2"}),t[13]||(t[13]=_(" 新建检测 "))]),e("button",{onClick:s,class:"inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card text-gray-700 dark:text-dark-text font-medium rounded-lg border border-gray-300 dark:border-dark-border hover:bg-gray-50 dark:hover:bg-dark-border shadow-sm hover:shadow-md transition-all duration-200"},[r(W(de),{class:"w-5 h-5 mr-2"}),t[14]||(t[14]=_(" 导出表格 "))]),D.value.length>0?(m(),x("div",pr,[e("span",vr," 已选择 "+n(D.value.length)+" 项 ",1),e("button",{onClick:g,class:"inline-flex items-center px-3 py-1.5 bg-blue-500 hover:bg-blue-600 text-white text-sm font-medium rounded-lg transition-all duration-200"},[r(W(de),{class:"w-4 h-4 mr-1"}),t[15]||(t[15]=_(" 批量导出 "))])])):N("",!0)])])]),e("div",br,[t[25]||(t[25]=e("div",{class:"px-6 py-4 border-b border-gray-100 dark:border-dark-border"},[e("h3",{class:"text-lg font-semibold text-gray-900 dark:text-dark-text"},"检测任务列表"),e("p",{class:"text-sm text-gray-600 dark:text-dark-text-secondary mt-1"},"管理和监控您的所有侵权检测任务")],-1)),e("div",yr,[ie((m(),ae(re,{data:c.value,style:{width:"100%"},"header-cell-style":{backgroundColor:"var(--el-bg-color-page)",color:"var(--el-text-color-primary)",fontWeight:"600",borderBottom:"1px solid var(--el-border-color-light)"},"row-style":{backgroundColor:"transparent"},class:"modern-table",onSelectionChange:A},{default:l(()=>[r(v,{type:"selection",width:"55",align:"center"}),r(v,{prop:"id",label:"检测ID",width:"120"},{default:l(d=>[e("div",fr,n(d.row.id),1)]),_:1}),r(v,{label:"检测类型",width:"200"},{default:l(()=>t[16]||(t[16]=[e("div",{class:"flex items-center space-x-3"},[e("div",{class:"w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center"},[e("svg",{class:"w-4 h-4 text-blue-600 dark:text-blue-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"})])]),e("div",null,[e("div",{class:"font-medium text-gray-900 dark:text-dark-text"},"侵权检测"),e("div",{class:"text-sm text-gray-500 dark:text-dark-text-secondary"},"AI智能检测")])],-1)])),_:1}),r(v,{label:"检测数量",width:"150"},{default:l(d=>[e("div",hr,[e("div",_r,[t[17]||(t[17]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"目标:",-1)),e("span",wr,n(d.row.imageCount),1)]),e("div",Cr,[t[18]||(t[18]=e("span",{class:"text-xs text-gray-500 dark:text-dark-text-secondary"},"完成:",-1)),e("span",$r,n(d.row.completedCount),1)]),e("div",Rr,[e("div",{class:"bg-green-500 h-1.5 rounded-full transition-all duration-300",style:ve({width:`${d.row.completedCount/d.row.imageCount*100}%`})},null,4)])])]),_:1}),r(v,{label:"检测状态",width:"150"},{default:l(d=>[e("div",Sr,[e("span",{class:le(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium",E(d.row.status)])},[e("span",{class:le(["w-1.5 h-1.5 rounded-full mr-1.5",G(d.row.status)])},null,2),_(" "+n(W(ye)(d.row.status)),1)],2),d.row.status===W(M).FAILED?(m(),x("div",Ir,[e("button",{onClick:me=>i(d.row),class:"text-xs text-red-600 dark:text-red-400 hover:text-red-700 dark:hover:text-red-300 underline"}," 查看原因 ",8,Vr)])):N("",!0)])]),_:1}),r(v,{label:"风险分布",width:"200"},{default:l(d=>[d.row.riskSummary?(m(),x("div",Dr,[d.row.riskSummary.noRisk>0?(m(),x("span",Tr," 无风险: "+n(d.row.riskSummary.noRisk),1)):N("",!0),d.row.riskSummary.lowRisk>0?(m(),x("span",Lr," 低风险: "+n(d.row.riskSummary.lowRisk),1)):N("",!0),d.row.riskSummary.mediumRisk>0?(m(),x("span",jr," 中风险: "+n(d.row.riskSummary.mediumRisk),1)):N("",!0),d.row.riskSummary.highRisk>0?(m(),x("span",Mr," 高风险: "+n(d.row.riskSummary.highRisk),1)):N("",!0)])):(m(),x("span",zr,"-"))]),_:1}),r(v,{prop:"operator",label:"创建人",width:"100"},{default:l(d=>[e("div",Br,n(d.row.operator),1)]),_:1}),r(v,{prop:"createTime",label:"创建时间",width:"160"},{default:l(d=>[e("div",Er,n(d.row.createTime),1)]),_:1}),r(v,{label:"操作",width:"180"},{default:l(d=>[e("div",Nr,[e("button",{onClick:me=>K(d.row),class:"inline-flex items-center px-3 py-1.5 text-sm font-medium text-primary-600 dark:text-primary-400 hover:text-primary-700 dark:hover:text-primary-300 bg-primary-50 dark:bg-primary-900/20 hover:bg-primary-100 dark:hover:bg-primary-900/30 rounded-lg transition-all duration-200"}," 查看详情 ",8,Pr),r(te,{onCommand:O,trigger:"click"},{dropdown:l(()=>[r(H,null,{default:l(()=>[r($,{command:{action:"smartCrop",row:d.row}},{default:l(()=>t[19]||(t[19]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M7 4V2a1 1 0 011-1h8a1 1 0 011 1v2m0 0V1a1 1 0 011-1h2a1 1 0 011 1v18a1 1 0 01-1 1H4a1 1 0 01-1-1V1a1 1 0 011-1h2a1 1 0 011 1v3m0 0h8m-8 0H4a1 1 0 00-1 1v3a1 1 0 001 1h3m0 0h10m0 0h3a1 1 0 001-1V5a1 1 0 00-1-1h-3m-3 0v8a1 1 0 01-1 1H8a1 1 0 01-1-1V4"})]),e("span",null,"智能裁图")],-1)])),_:2,__:[19]},1032,["command"]),r($,{command:{action:"titleGenerate",row:d.row}},{default:l(()=>t[20]||(t[20]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})]),e("span",null,"标题生成")],-1)])),_:2,__:[20]},1032,["command"]),r($,{command:{action:"batchListing",row:d.row}},{default:l(()=>t[21]||(t[21]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"})]),e("span",null,"批量刊登")],-1)])),_:2,__:[21]},1032,["command"]),r($,{command:{action:"oneClickCutout",row:d.row}},{default:l(()=>t[22]||(t[22]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-pink-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"})]),e("span",null,"一键抠图")],-1)])),_:2,__:[22]},1032,["command"]),r($,{command:{action:"superSplit",row:d.row}},{default:l(()=>t[23]||(t[23]=[e("div",{class:"flex items-center space-x-2"},[e("svg",{class:"w-4 h-4 text-orange-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M13 10V3L4 14h7v7l9-11h-7z"})]),e("span",null,"超级裂变")],-1)])),_:2,__:[23]},1032,["command"])]),_:2},1024)]),default:l(()=>[t[24]||(t[24]=e("button",{class:"inline-flex items-center px-2 py-1.5 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-gray-700 dark:hover:text-dark-text bg-gray-50 dark:bg-dark-card hover:bg-gray-100 dark:hover:bg-dark-border rounded-lg transition-all duration-200"},[_(" 更多 "),e("svg",{class:"w-4 h-4 ml-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24"},[e("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 9l-7 7-7-7"})])],-1))]),_:2,__:[24]},1024)])]),_:1})]),_:1},8,["data"])),[[X,I.value]])]),e("div",Gr,[e("div",Hr," 共 "+n(y.value.total)+" 条记录 ",1),r(R,{"current-page":y.value.currentPage,"onUpdate:currentPage":t[1]||(t[1]=d=>y.value.currentPage=d),"page-size":y.value.pageSize,"onUpdate:pageSize":t[2]||(t[2]=d=>y.value.pageSize=d),"page-sizes":[10,20,50,100],total:y.value.total,layout:"sizes, prev, pager, next, jumper",onSizeChange:J,onCurrentChange:w,class:"modern-pagination"},null,8,["current-page","page-size","total"])])])]),r(Ne,{modelValue:V.value,"onUpdate:modelValue":t[3]||(t[3]=d=>V.value=d),onSuccess:o},null,8,["modelValue"]),r(Yt,{modelValue:z.value,"onUpdate:modelValue":t[4]||(t[4]=d=>z.value=d),task:C.value},null,8,["modelValue","task"])],64)}}}),Or=oe(Ur,[["__scopeId","data-v-d133d237"]]);export{Or as default};
