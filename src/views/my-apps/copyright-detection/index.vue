<template>
  <div class="space-y-6">
    <!-- 页面标题 -->
    <div class="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 rounded-2xl p-6 border border-blue-100 dark:border-blue-800">
      <div class="flex items-center space-x-3">
        <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
          <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
          </svg>
        </div>
        <div>
          <h1 class="text-2xl font-bold text-gray-900 dark:text-dark-text">侵权检测</h1>
          <p class="mt-1 text-sm text-gray-600 dark:text-dark-text-secondary">AI智能图片侵权检测和风险评估工具</p>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">无风险</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ stats.noRisk }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-yellow-100 dark:bg-yellow-900/30 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-yellow-600 dark:text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">低风险</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ stats.lowRisk }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">中风险</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ stats.mediumRisk }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
        <div class="flex items-center">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 bg-red-100 dark:bg-red-900/30 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600 dark:text-dark-text-secondary">高风险</p>
            <p class="text-2xl font-bold text-gray-900 dark:text-dark-text">{{ stats.highRisk }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作区域 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl p-6 shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border">
      <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div class="flex items-center space-x-3">
          <button @click="showCreateDialog = true"
            class="inline-flex items-center px-4 py-2.5 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-medium rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105">
            <PlusIcon class="w-5 h-5 mr-2" />
            新建检测
          </button>
          <button @click="exportTable"
            class="inline-flex items-center px-4 py-2.5 bg-white dark:bg-dark-card border border-gray-300 dark:border-dark-border text-gray-700 dark:text-dark-text hover:bg-gray-50 dark:hover:bg-dark-hover font-medium rounded-lg shadow-sm hover:shadow-md transition-all duration-200">
            <ArrowDownTrayIcon class="w-5 h-5 mr-2" />
            导出表格
          </button>
        </div>

        <!-- 筛选区域 -->
        <div class="flex items-center space-x-3">
          <!-- 状态筛选 -->
          <el-select v-model="statusFilter" placeholder="状态筛选" clearable style="width: 120px" @change="handleSearch">
            <el-option label="等待中" value="pending" />
            <el-option label="检测中" value="processing" />
            <el-option label="已完成" value="completed" />
            <el-option label="失败" value="failed" />
          </el-select>

          <!-- 风险等级筛选 -->
          <el-select v-model="riskFilter" placeholder="风险等级" multiple clearable style="width: 140px" @change="handleSearch">
            <el-option label="无风险" value="no_risk" />
            <el-option label="低风险" value="low_risk" />
            <el-option label="中风险" value="medium_risk" />
            <el-option label="高风险" value="high_risk" />
          </el-select>

          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索任务ID..."
            style="width: 200px"
            @input="handleSearch"
            clearable>
            <template #prefix>
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
              </svg>
            </template>
          </el-input>
        </div>
      </div>

      <!-- 批量操作 -->
      <div v-if="selectedRows.length > 0" class="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
        <div class="flex items-center justify-between">
          <span class="text-sm text-blue-700 dark:text-blue-300">
            已选择 {{ selectedRows.length }} 个任务
          </span>
          <div class="flex space-x-2">
            <button @click="batchExport"
              class="inline-flex items-center px-3 py-1.5 bg-blue-600 hover:bg-blue-700 text-white text-sm font-medium rounded-md transition-colors duration-200">
              <ArrowDownTrayIcon class="w-4 h-4 mr-1" />
              批量导出
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 任务列表 -->
    <div class="bg-white dark:bg-dark-surface rounded-xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border overflow-hidden">
      <el-table
        :data="filteredTasks"
        v-loading="loading"
        @selection-change="handleSelectionChange"
        style="width: 100%"
        class="modern-table">
        
        <el-table-column type="selection" width="55" align="center" />
        
        <el-table-column prop="id" label="任务ID" width="100" align="center" />
        
        <el-table-column label="图片数量" width="120" align="center">
          <template #default="scope">
            <span class="font-medium">{{ scope.row.imageCount }}</span>
          </template>
        </el-table-column>

        <el-table-column label="检测进度" width="150" align="center">
          <template #default="scope">
            <div class="flex flex-col items-center space-y-1">
              <span class="text-sm font-medium">{{ scope.row.completedCount }}/{{ scope.row.imageCount }}</span>
              <el-progress 
                :percentage="Math.round((scope.row.completedCount / scope.row.imageCount) * 100)" 
                :stroke-width="6"
                :show-text="false"
                class="w-full" />
            </div>
          </template>
        </el-table-column>

        <el-table-column label="风险分布" width="200" align="center">
          <template #default="scope">
            <div v-if="scope.row.riskSummary" class="flex justify-center space-x-2">
              <el-tag v-if="scope.row.riskSummary.noRisk > 0" type="success" size="small">
                无风险: {{ scope.row.riskSummary.noRisk }}
              </el-tag>
              <el-tag v-if="scope.row.riskSummary.lowRisk > 0" type="warning" size="small">
                低风险: {{ scope.row.riskSummary.lowRisk }}
              </el-tag>
              <el-tag v-if="scope.row.riskSummary.mediumRisk > 0" type="danger" size="small">
                中风险: {{ scope.row.riskSummary.mediumRisk }}
              </el-tag>
              <el-tag v-if="scope.row.riskSummary.highRisk > 0" type="danger" size="small">
                高风险: {{ scope.row.riskSummary.highRisk }}
              </el-tag>
            </div>
            <span v-else class="text-gray-400 dark:text-dark-text-secondary">-</span>
          </template>
        </el-table-column>

        <el-table-column label="状态" width="100" align="center">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="operator" label="操作员" width="100" align="center" />
        
        <el-table-column prop="createTime" label="创建时间" width="160" align="center" />

        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="scope">
            <div class="flex justify-center space-x-2">
              <el-button 
                type="primary" 
                size="small" 
                @click="viewDetails(scope.row)"
                :disabled="scope.row.status !== 'completed'">
                查看详情
              </el-button>
              
              <el-dropdown @command="handleMoreAction" trigger="click">
                <el-button type="info" size="small">
                  更多
                  <svg class="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{ action: 'titleGenerate', row: scope.row }">标题生成</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'batchListing', row: scope.row }">批量刊登</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'smartCrop', row: scope.row }">智能裁图</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'oneClickCutout', row: scope.row }">一键抠图</el-dropdown-item>
                    <el-dropdown-item :command="{ action: 'superSplit', row: scope.row }">超级裂变</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="flex justify-between items-center px-6 py-4 border-t border-gray-100 dark:border-dark-border bg-gray-50 dark:bg-dark-card/50">
        <div class="text-sm text-gray-600 dark:text-dark-text-secondary">
          共 {{ pagination.total }} 条记录
        </div>
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="modern-pagination"
        />
      </div>
    </div>
  </div>

  <!-- 新建检测对话框 -->
  <CreateDetectionDialog v-model="showCreateDialog" @success="refreshData" />

  <!-- 查看详情对话框 -->
  <ViewDetailsDialog v-model="showDetailsDialog" :task="selectedTask" />
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import { PlusIcon, ArrowDownTrayIcon } from '@heroicons/vue/24/outline';
import CreateDetectionDialog from './components/CreateDetectionDialog.vue';
import ViewDetailsDialog from './components/ViewDetailsDialog.vue';
import {
  copyrightDetectionStore,
  getStatusText,
  type CopyrightDetectionTask,
  DetectionStatus
} from '@/store/copyright-detection';

// 响应式数据
const loading = ref(false);
const showCreateDialog = ref(false);
const showDetailsDialog = ref(false);
const selectedTask = ref<CopyrightDetectionTask | null>(null);
const selectedRows = ref<CopyrightDetectionTask[]>([]);

// 筛选条件
const statusFilter = ref('');
const riskFilter = ref<string[]>([]);
const searchKeyword = ref('');

// 分页
const pagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 计算属性
const stats = computed(() => {
  const completedTasks = copyrightDetectionStore.tasks.filter(task => task.status === DetectionStatus.COMPLETED);
  return completedTasks.reduce((acc, task) => {
    if (task.riskSummary) {
      acc.noRisk += task.riskSummary.noRisk;
      acc.lowRisk += task.riskSummary.lowRisk;
      acc.mediumRisk += task.riskSummary.mediumRisk;
      acc.highRisk += task.riskSummary.highRisk;
    }
    return acc;
  }, { noRisk: 0, lowRisk: 0, mediumRisk: 0, highRisk: 0 });
});

const filteredTasks = computed(() => {
  let filtered = copyrightDetectionStore.tasks;

  // 状态筛选
  if (statusFilter.value) {
    filtered = filtered.filter(task => task.status === statusFilter.value);
  }

  // 风险等级筛选
  if (riskFilter.value.length > 0) {
    filtered = filtered.filter(task => {
      if (!task.riskSummary) return false;
      return riskFilter.value.some(level => {
        switch (level) {
          case 'no_risk':
            return task.riskSummary!.noRisk > 0;
          case 'low_risk':
            return task.riskSummary!.lowRisk > 0;
          case 'medium_risk':
            return task.riskSummary!.mediumRisk > 0;
          case 'high_risk':
            return task.riskSummary!.highRisk > 0;
          default:
            return false;
        }
      });
    });
  }

  // 搜索筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    filtered = filtered.filter(task =>
      task.id.toLowerCase().includes(keyword) ||
      task.operator.toLowerCase().includes(keyword)
    );
  }

  pagination.value.total = filtered.length;

  const start = (pagination.value.currentPage - 1) * pagination.value.pageSize;
  const end = start + pagination.value.pageSize;
  return filtered.slice(start, end);
});

// 生命周期
onMounted(() => {
  loadTasks();
});

// 方法
const loadTasks = () => {
  loading.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 500);
};

const getStatusType = (status: string) => {
  const statusTypes: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'failed': 'danger'
  };
  return statusTypes[status] || 'info';
};

const handleSearch = () => {
  pagination.value.currentPage = 1;
};

const handleSelectionChange = (selection: CopyrightDetectionTask[]) => {
  selectedRows.value = selection;
};

const viewDetails = (task: CopyrightDetectionTask) => {
  selectedTask.value = task;
  showDetailsDialog.value = true;
};

const handleMoreAction = (command: { action: string; row: CopyrightDetectionTask }) => {
  const { action, row } = command;

  switch (action) {
    case 'titleGenerate':
      createTitleGenerateTask(row);
      break;
    case 'batchListing':
      createBatchListingTask(row);
      break;
    case 'smartCrop':
      createSmartCropTask(row);
      break;
    case 'oneClickCutout':
      createOneClickCutoutTask(row);
      break;
    case 'superSplit':
      createSuperSplitTask(row);
      break;
  }
};

const exportTable = () => {
  ElMessage.success('导出表格功能开发中...');
};

const batchExport = () => {
  ElMessage.success(`正在批量导出 ${selectedRows.value.length} 个任务...`);
};

const refreshData = () => {
  ElMessage.success('侵权检测任务创建成功！');
  loadTasks();
};

// 创建其他任务的方法
const createTitleGenerateTask = (detectionData: CopyrightDetectionTask) => {
  ElMessage.success(`正在为检测任务 ${detectionData.id} 创建标题生成任务...`);
};

const createBatchListingTask = (detectionData: CopyrightDetectionTask) => {
  ElMessage.success(`正在为检测任务 ${detectionData.id} 创建批量刊登任务...`);
};

const createSmartCropTask = (detectionData: CopyrightDetectionTask) => {
  ElMessage.success(`正在为检测任务 ${detectionData.id} 创建智能裁图任务...`);
};

const createOneClickCutoutTask = (detectionData: CopyrightDetectionTask) => {
  ElMessage.success(`正在为检测任务 ${detectionData.id} 创建一键抠图任务...`);
};

const createSuperSplitTask = (detectionData: CopyrightDetectionTask) => {
  ElMessage.success(`正在为检测任务 ${detectionData.id} 创建超级裂变任务...`);
};

// 分页相关方法
const handleSizeChange = (val: number) => {
  pagination.value.pageSize = val;
  pagination.value.currentPage = 1;
  loadTasks();
};

const handleCurrentChange = (val: number) => {
  pagination.value.currentPage = val;
  loadTasks();
};
</script>

<style scoped>
.modern-table {
  width: 100% !important;
  --el-table-border-color: theme('colors.gray.100');
  --el-table-bg-color: theme('colors.white');
  --el-table-tr-bg-color: theme('colors.white');
  --el-table-expanded-cell-bg-color: theme('colors.gray.50');
}

.modern-table :deep(.el-table) {
  width: 100% !important;
}

.modern-table :deep(.el-table__header-wrapper) {
  background: theme('colors.gray.50');
}

.modern-table :deep(.el-table__header) {
  background: theme('colors.gray.50');
}

.modern-table :deep(.el-table th) {
  background: theme('colors.gray.50') !important;
  color: theme('colors.gray.700');
  font-weight: 600;
  border-bottom: 1px solid theme('colors.gray.200');
}

.modern-table :deep(.el-table td) {
  border-bottom: 1px solid theme('colors.gray.100');
}

.modern-table :deep(.el-table__row:hover > td) {
  background-color: theme('colors.blue.50') !important;
}

.modern-pagination :deep(.el-pagination) {
  --el-pagination-font-size: 14px;
  --el-pagination-bg-color: theme('colors.white');
  --el-pagination-text-color: theme('colors.gray.600');
  --el-pagination-border-radius: 6px;
  --el-pagination-button-color: theme('colors.gray.600');
  --el-pagination-button-bg-color: theme('colors.white');
  --el-pagination-button-disabled-color: theme('colors.gray.400');
  --el-pagination-button-disabled-bg-color: theme('colors.gray.100');
  --el-pagination-hover-color: theme('colors.blue.600');
}

/* 暗色模式适配 */
.dark .modern-table {
  --el-table-border-color: theme('colors.gray.700');
  --el-table-bg-color: theme('colors.gray.800');
  --el-table-tr-bg-color: theme('colors.gray.800');
  --el-table-expanded-cell-bg-color: theme('colors.gray.700');
}

.dark .modern-table :deep(.el-table__header-wrapper) {
  background: theme('colors.gray.700');
}

.dark .modern-table :deep(.el-table__header) {
  background: theme('colors.gray.700');
}

.dark .modern-table :deep(.el-table th) {
  background: theme('colors.gray.700') !important;
  color: theme('colors.gray.200');
  border-bottom: 1px solid theme('colors.gray.600');
}

.dark .modern-table :deep(.el-table td) {
  background: theme('colors.gray.800') !important;
  color: theme('colors.gray.200');
  border-bottom: 1px solid theme('colors.gray.700');
}

.dark .modern-table :deep(.el-table__row:hover > td) {
  background-color: theme('colors.gray.700') !important;
}
</style>
