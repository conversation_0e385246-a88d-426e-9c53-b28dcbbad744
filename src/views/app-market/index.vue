<template>
  <div class="space-y-8">
    <!-- 页面标题 -->
    <div class="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-2xl p-8 border border-purple-100 dark:border-purple-800">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-6">
          <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-600 rounded-2xl flex items-center justify-center shadow-xl transform hover:scale-105 transition-transform duration-200">
            <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
            </svg>
          </div>
          <div>
            <h1 class="text-3xl font-bold text-gray-900 dark:text-dark-text">应用市场</h1>
            <p class="mt-2 text-base text-gray-600 dark:text-dark-text-secondary">发现、安装和管理您的应用工具</p>
            <div class="flex items-center mt-3 space-x-4">
              <div class="flex items-center text-sm text-purple-600 dark:text-purple-400">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                </svg>
                <span>{{ totalApps }} 个应用</span>
              </div>
              <div class="flex items-center text-sm text-green-600 dark:text-green-400">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                </svg>
                <span>{{ favoriteCount }} 个收藏</span>
              </div>
              <div class="flex items-center text-sm text-blue-600 dark:text-blue-400">
                <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
                <span>{{ installedCount }} 个已安装</span>
              </div>
            </div>
          </div>
        </div>
        <div class="hidden lg:flex items-center space-x-3">
          <button
            @click="refreshApps"
            class="px-4 py-2 text-sm font-medium text-purple-600 dark:text-purple-400 bg-white dark:bg-dark-surface hover:bg-purple-50 dark:hover:bg-purple-900/20 rounded-xl border border-purple-200 dark:border-purple-700 transition-all duration-200 transform hover:scale-105"
          >
            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
            </svg>
            刷新
          </button>
        </div>
      </div>
    </div>

    <!-- 分类导航 -->
    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6">
      <div class="flex items-center justify-between mb-6">
        <h2 class="text-xl font-bold text-gray-900 dark:text-dark-text flex items-center">
          <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"></path>
          </svg>
          应用分类
        </h2>
        <div class="text-sm text-gray-500 dark:text-dark-text-secondary">
          {{ filteredApps.length }} / {{ totalApps }} 个应用
        </div>
      </div>

      <!-- 分类标签 -->
      <div class="flex flex-wrap gap-3">
        <button
          @click="selectedCategory = ''; handleCategoryChange()"
          :class="[
            'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-105',
            selectedCategory === ''
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
              : 'bg-gray-100 dark:bg-dark-surface text-gray-700 dark:text-dark-text hover:bg-gray-200 dark:hover:bg-dark-border'
          ]"
        >
          <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
          </svg>
          全部应用
          <span class="ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs">{{ totalApps }}</span>
        </button>

        <button
          v-for="category in categoryOptions"
          :key="category.value"
          @click="selectedCategory = category.value; handleCategoryChange()"
          :class="[
            'px-4 py-2 rounded-xl text-sm font-medium transition-all duration-200 transform hover:scale-105 flex items-center',
            selectedCategory === category.value
              ? 'bg-gradient-to-r from-purple-500 to-pink-500 text-white shadow-lg'
              : 'bg-gray-100 dark:bg-dark-surface text-gray-700 dark:text-dark-text hover:bg-gray-200 dark:hover:bg-dark-border'
          ]"
        >
          <component :is="category.icon" class="w-4 h-4 mr-2" />
          {{ category.label }}
          <span class="ml-2 px-2 py-0.5 bg-white/20 rounded-full text-xs">{{ getCategoryCount(category.value) }}</span>
        </button>
      </div>
    </div>

    <!-- 搜索和筛选区域 -->
    <div class="bg-white dark:bg-dark-card rounded-2xl shadow-elegant dark:shadow-elegant-dark border border-gray-100 dark:border-dark-border p-6">
      <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
        <!-- 搜索框 -->
        <div class="flex-1 max-w-2xl">
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
              <MagnifyingGlassIcon class="w-5 h-5 text-gray-400" />
            </div>
            <input
              v-model="searchKeyword"
              @input="handleSearch"
              type="text"
              placeholder="搜索应用名称、描述或标签..."
              class="w-full pl-12 pr-12 py-4 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-2xl focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 placeholder-gray-400 dark:placeholder-gray-500 text-gray-900 dark:text-dark-text hover:border-gray-300 dark:hover:border-dark-text-secondary"
            />
            <button
              v-if="searchKeyword"
              @click="searchKeyword = ''; handleSearch()"
              class="absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors duration-200"
            >
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>
        </div>

        <!-- 筛选器 -->
        <div class="flex flex-wrap items-center gap-4">
          <!-- 价格类型筛选 -->
          <div class="relative">
            <select
              v-model="selectedPriceType"
              @change="handlePriceTypeChange"
              class="appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"
            >
              <option value="">全部价格</option>
              <option
                v-for="priceType in priceTypeOptions"
                :key="priceType.value"
                :value="priceType.value"
              >
                {{ priceType.label }}
              </option>
            </select>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>

          <!-- 评分筛选 -->
          <div class="relative">
            <select
              v-model="selectedRating"
              @change="handleRatingChange"
              class="appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"
            >
              <option value="">全部评分</option>
              <option :value="4">4星以上</option>
              <option :value="3">3星以上</option>
              <option :value="2">2星以上</option>
            </select>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
              <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
              </svg>
            </div>
          </div>

          <!-- 排序 -->
          <div class="flex items-center space-x-2">
            <div class="relative">
              <select
                v-model="sortBy"
                @change="handleSortChange"
                class="appearance-none px-4 py-3 pr-10 bg-gray-50 dark:bg-dark-surface border border-gray-200 dark:border-dark-border rounded-xl text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 hover:border-gray-300 dark:hover:border-dark-text-secondary cursor-pointer"
              >
                <option value="name">按名称</option>
                <option value="rating">按评分</option>
                <option value="downloadCount">按下载量</option>
                <option value="lastUpdated">按更新时间</option>
                <option value="price">按价格</option>
              </select>
              <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                </svg>
              </div>
            </div>
            <button
              @click="toggleSortOrder"
              class="p-3 rounded-xl bg-gray-50 dark:bg-dark-surface hover:bg-gray-100 dark:hover:bg-dark-border transition-all duration-200 border border-gray-200 dark:border-dark-border transform hover:scale-105"
              :title="sortOrder === 'asc' ? '升序' : '降序'"
            >
              <component :is="sortOrder === 'asc' ? ArrowUpIcon : ArrowDownIcon" class="w-4 h-4 text-gray-600 dark:text-dark-text-secondary" />
            </button>
          </div>

          <!-- 清除筛选 -->
          <button
            @click="clearAllFilters"
            class="px-4 py-3 text-sm font-medium text-gray-600 dark:text-dark-text-secondary hover:text-purple-600 dark:hover:text-purple-400 bg-gray-50 dark:bg-dark-surface hover:bg-gray-100 dark:hover:bg-dark-border rounded-xl transition-all duration-200 border border-gray-200 dark:border-dark-border transform hover:scale-105"
          >
            <svg class="w-4 h-4 mr-2 inline" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
            清除筛选
          </button>
        </div>
      </div>
    </div>

    <!-- 标签页 -->
    <div class="bg-white dark:bg-dark-card rounded-xl shadow-sm border border-gray-100 dark:border-dark-border overflow-hidden">
      <el-tabs v-model="activeTab" class="app-market-tabs">
        <el-tab-pane label="全部应用" name="all">
          <AppGrid
            :apps="filteredApps"
            :loading="loading"
            @app-click="handleAppClick"
            @favorite-toggle="handleFavoriteToggle"
            @install-toggle="handleInstallToggle"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
        <el-tab-pane name="favorites">
          <template #label>
            <span class="flex items-center">
              我的收藏
              <el-badge :value="favoriteCount" :hidden="favoriteCount === 0" class="ml-2" />
            </span>
          </template>
          <AppGrid
            :apps="favoriteApps"
            :loading="loading"
            @app-click="handleAppClick"
            @favorite-toggle="handleFavoriteToggle"
            @install-toggle="handleInstallToggle"
            @purchase="handlePurchase"
          />
        </el-tab-pane>
      </el-tabs>
    </div>

    <!-- 应用详情弹窗 -->
    <AppDetailsDialog
      v-model="showDetailsDialog"
      :app="selectedApp"
      @favorite-toggle="handleFavoriteToggle"
      @install-toggle="handleInstallToggle"
    />

    <!-- 购买弹窗 -->
    <PurchaseDialog
      v-model="showPurchaseDialog"
      :app="selectedPurchaseApp"
      @purchase-success="handlePurchaseSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import {
  MagnifyingGlassIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PhotoIcon,
  ChartBarIcon,
  MagnifyingGlassCircleIcon,
  PresentationChartLineIcon,
  Cog6ToothIcon,
  BoltIcon,
  PencilIcon
} from '@heroicons/vue/24/outline';
import AppGrid from './components/AppGrid.vue';
import AppDetailsDialog from './components/AppDetailsDialog.vue';
import PurchaseDialog from './components/PurchaseDialog.vue';
import {
  initAppMarket,
  getAllApps,
  getFavoriteApps,
  getFilteredApps,
  setFilter,
  clearFilter,
  toggleFavorite,
  toggleInstall,
  getAppById,
  appMarketStore,
  AppCategory,
  PriceType,
  type AppInfo,
  type FilterOptions
} from '../../store/app-market';

// 响应式数据
const activeTab = ref('all');
const searchKeyword = ref('');
const selectedCategory = ref('');
const selectedPriceType = ref('');
const selectedRating = ref<number | ''>('');
const sortBy = ref('name');
const sortOrder = ref<'asc' | 'desc'>('asc');
const showDetailsDialog = ref(false);
const selectedApp = ref<AppInfo | null>(null);
const showPurchaseDialog = ref(false);
const selectedPurchaseApp = ref<AppInfo | null>(null);

// 计算属性
const loading = computed(() => appMarketStore.loading.value);
const totalApps = computed(() => getAllApps().length);
const favoriteCount = computed(() => getFavoriteApps.value.length);
const installedCount = computed(() => appMarketStore.installedApps.value.length);
const filteredApps = computed(() => getFilteredApps.value);
const favoriteApps = computed(() => getFavoriteApps.value);

// 分类选项
const categoryOptions = computed(() => [
  { label: '图像处理', value: AppCategory.IMAGE_PROCESSING, icon: PhotoIcon },
  { label: '数据分析', value: AppCategory.DATA_ANALYSIS, icon: ChartBarIcon },
  { label: 'SEO工具', value: AppCategory.SEO_TOOLS, icon: MagnifyingGlassCircleIcon },
  { label: '市场分析', value: AppCategory.MARKET_ANALYSIS, icon: PresentationChartLineIcon },
  { label: '管理工具', value: AppCategory.MANAGEMENT_TOOLS, icon: Cog6ToothIcon },
  { label: '自动化工具', value: AppCategory.AUTOMATION, icon: BoltIcon },
  { label: '内容创作', value: AppCategory.CONTENT_CREATION, icon: PencilIcon }
]);

// 价格类型选项
const priceTypeOptions = computed(() => [
  { label: '免费', value: PriceType.FREE },
  { label: '一口价', value: PriceType.ONE_TIME },
  { label: '包月', value: PriceType.MONTHLY },
  { label: '按次计费', value: PriceType.PER_USE }
]);

// 方法
const handleSearch = () => {
  updateFilter();
};

const handleCategoryChange = () => {
  updateFilter();
};

const handlePriceTypeChange = () => {
  updateFilter();
};

const handleRatingChange = () => {
  updateFilter();
};

const handleSortChange = () => {
  updateFilter();
};

const toggleSortOrder = () => {
  sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
  updateFilter();
};

const updateFilter = () => {
  const filter: FilterOptions = {
    searchKeyword: searchKeyword.value || undefined,
    category: selectedCategory.value as AppCategory || undefined,
    priceType: selectedPriceType.value as PriceType || undefined,
    rating: selectedRating.value as number || undefined,
    sortBy: sortBy.value as any,
    sortOrder: sortOrder.value
  };
  setFilter(filter);
};

const clearAllFilters = () => {
  searchKeyword.value = '';
  selectedCategory.value = '';
  selectedPriceType.value = '';
  selectedRating.value = '';
  sortBy.value = 'name';
  sortOrder.value = 'asc';
  clearFilter();
};

const handleAppClick = (app: AppInfo) => {
  selectedApp.value = app;
  showDetailsDialog.value = true;
};

const handleFavoriteToggle = (appId: string) => {
  const isFavorited = toggleFavorite(appId);
  const app = getAppById(appId);
  if (app) {
    ElMessage.success(
      isFavorited ? `已收藏 ${app.name}` : `已取消收藏 ${app.name}`
    );
  }
};

const handleInstallToggle = (appId: string) => {
  const isInstalled = toggleInstall(appId);
  const app = getAppById(appId);
  if (app) {
    ElMessage.success(
      isInstalled ? `已安装 ${app.name}` : `已卸载 ${app.name}`
    );
  }
};

const handlePurchase = (app: AppInfo) => {
  selectedPurchaseApp.value = app;
  showPurchaseDialog.value = true;
};

const handlePurchaseSuccess = (appId: string) => {
  // 购买成功后自动安装应用
  handleInstallToggle(appId);
  showPurchaseDialog.value = false;
};

// 获取分类应用数量
const getCategoryCount = (category: string) => {
  if (!category) return totalApps.value;
  return getAllApps().filter(app => app.category === category).length;
};

// 刷新应用数据
const refreshApps = () => {
  ElMessage.success('应用数据已刷新');
  // 这里可以添加实际的刷新逻辑
};

// 生命周期
onMounted(() => {
  initAppMarket();
});
</script>

<style scoped>
/* 应用市场标签页样式 */
:deep(.app-market-tabs) {
  .el-tabs__header {
    margin: 0;
    border-bottom: 1px solid var(--el-border-color-light);
    background: transparent;
  }

  .el-tabs__nav-wrap {
    padding: 0 24px;
  }

  .el-tabs__item {
    height: 48px;
    line-height: 48px;
    font-weight: 500;
    color: var(--el-text-color-regular);
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
  }

  .el-tabs__item:hover {
    color: var(--el-color-primary);
  }

  .el-tabs__item.is-active {
    color: var(--el-color-primary);
    border-bottom-color: var(--el-color-primary);
  }

  .el-tabs__content {
    padding: 24px;
  }
}

/* 暗黑模式适配 */
.dark :deep(.app-market-tabs) {
  .el-tabs__header {
    border-bottom-color: var(--el-border-color-dark);
  }

  .el-tabs__item {
    color: var(--el-text-color-regular);
  }

  .el-tabs__item:hover,
  .el-tabs__item.is-active {
    color: var(--el-color-primary);
  }
}

/* 筛选器样式 */
.filter-group {
  @apply flex flex-col space-y-2;
}

.filter-label {
  @apply text-sm font-medium text-gray-700 dark:text-dark-text-secondary;
}

.filter-select {
  @apply px-3 py-2 bg-gray-50 dark:bg-dark-card border border-gray-200 dark:border-dark-border rounded-lg text-sm text-gray-900 dark:text-dark-text focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent transition-all duration-200;
}

.filter-select:hover {
  @apply bg-gray-100 dark:bg-dark-border;
}

/* 优雅阴影效果 */
.shadow-elegant {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.shadow-elegant-dark {
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.3), 0 1px 2px 0 rgba(0, 0, 0, 0.2);
}

/* 分类按钮动画效果 */
.category-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.category-button.active {
  transform: translateY(-1px);
  box-shadow: 0 10px 30px rgba(147, 51, 234, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

@media (max-width: 768px) {
  .grid-cols-1.md\\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }
}
</style>
